import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    console.log('🔍 Debug auth request headers:', req.headers)
    
    const authHeader = req.headers.authorization
    const publishableKey = req.headers['x-publishable-api-key']
    
    console.log('🔑 Authorization header:', authHeader)
    console.log('🔑 Publishable API key:', publishableKey)
    
    // Try to get auth context if available
    let authContext = null
    try {
      authContext = (req as any).auth_context
      console.log('👤 Auth context:', authContext)
    } catch (error) {
      console.log('❌ No auth context available:', error.message)
    }

    res.json({
      success: true,
      debug_info: {
        has_auth_header: !!authHeader,
        auth_header_format: authHeader ? authHeader.substring(0, 20) + '...' : null,
        has_publishable_key: !!publishableKey,
        publishable_key_format: publishableKey ? publishableKey.substring(0, 20) + '...' : null,
        auth_context: authContext,
        all_headers: Object.keys(req.headers),
      },
      message: "Debug information for authentication",
    })
  } catch (error) {
    console.error('❌ Debug auth failed:', error)
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Debug auth failed",
    })
  }
}

export const POST = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    console.log('🔍 Authenticated debug request')
    console.log('👤 Auth context:', req.auth_context)
    
    res.json({
      success: true,
      authenticated: true,
      auth_context: req.auth_context,
      message: "Successfully authenticated request",
    })
  } catch (error) {
    console.error('❌ Authenticated debug failed:', error)
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Authenticated debug failed",
    })
  }
}
