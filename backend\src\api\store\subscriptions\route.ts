import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"

// GET current user subscription
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const bookService = req.scope.resolve(BOOK_MODULE)
  
  if (!req.user) {
    return res.status(401).json({ message: "Authentication required" })
  }

  try {
    const subscription = await bookService.getUserActiveSubscription(req.user.id)
    
    if (!subscription) {
      return res.json({ subscription: null })
    }

    res.json({
      subscription: {
        id: subscription.id,
        type: subscription.type,
        starts_at: subscription.starts_at,
        expires_at: subscription.expires_at,
        is_active: subscription.is_active,
        auto_renew: subscription.auto_renew,
        created_at: subscription.created_at
      }
    })
  } catch (error) {
    console.error("Error fetching subscription:", error)
    res.status(500).json({ message: "Internal server error" })
  }
}

// POST create new subscription (initiate payment)
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  if (!req.user) {
    return res.status(401).json({ message: "Authentication required" })
  }

  try {
    const { type } = req.body

    if (!type || !["6_months", "12_months"].includes(type)) {
      return res.status(400).json({ message: "Invalid subscription type" })
    }

    // Check if user already has active subscription
    const bookService = req.scope.resolve(BOOK_MODULE)
    const existingSubscription = await bookService.getUserActiveSubscription(req.user.id)
    
    if (existingSubscription) {
      return res.status(400).json({ message: "User already has an active subscription" })
    }

    // Calculate price based on type
    const prices = {
      "6_months": 29.99,
      "12_months": 49.99
    }

    const price = prices[type as keyof typeof prices]

    // Create Stripe checkout session
    const stripe = require("stripe")(process.env.STRIPE_API_KEY)
    
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `Hebrew Book Store Subscription - ${type === "6_months" ? "6 Months" : "12 Months"}`,
              description: "Access to all chapters and content",
            },
            unit_amount: Math.round(price), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: `${process.env.FRONTEND_URL}/account/subscription?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL}/account/subscription?cancelled=true`,
      metadata: {
        user_id: req.user.id,
        subscription_type: type,
        price: price.toString()
      },
    })

    res.json({ checkout_url: session.url, session_id: session.id })
  } catch (error) {
    console.error("Error creating subscription:", error)
    res.status(500).json({ message: "Internal server error" })
  }
}
