import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { Modules } from "@medusajs/framework/utils"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  // Set CORS headers first
  //  console.log('get request for ', req, "setting headers")

  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  res.setHeader('Access-Control-Allow-Credentials', 'true')

  try {
    // Get store service using the correct MedusaJS v2 way
    const storeModuleService = req.scope.resolve(Modules.STORE)
    const stores = await storeModuleService.listStores()
    
    if (stores.length === 0) {
      return res.status(404).json({ message: "Store not found" })
    }
    
    const store = stores[0]
    const metadata = store.metadata || {}
    
    // Return settings from store metadata
    const settings = {
      main_video_url: metadata.main_video_url || "https://www.youtube.com/embed/dQw4w9WgXcQ"
    }
    
    res.json({ settings })
  } catch (error) {
    console.error("Error fetching store settings:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}

export async function OPTIONS(req: MedusaRequest, res: MedusaResponse) {
  // Set CORS headers for preflight request
  console.log('OPTIONS request for ', req, "setting headers")
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  res.setHeader('Access-Control-Allow-Credentials', 'true')
  res.setHeader('Access-Control-Max-Age', '86400') // 24 hours

  res.status(200).end()
}
