import { 
  AuthenticatedMedusaRequest, 
  MedusaResponse,
} from "@medusajs/framework/http"
import { Modules } from "@medusajs/framework/utils"
import { IFileModuleService } from "@medusajs/framework/types"

export const POST = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const fileModuleService: IFileModuleService = req.scope.resolve(
    Modules.FILE
  )

  const files = req.files as Express.Multer.File[]

  if (!files || files.length === 0) {
    return res.status(400).json({
      error: "No files uploaded",
    })
  }

  try {
    const uploadedFiles = await Promise.all(
      files.map(async (file) => {
        const uploadedFile = await fileModuleService.createFiles({
          filename: file.originalname,
          mimeType: file.mimetype,
          content: file.buffer,
        })

        return {
          id: uploadedFile.id,
          url: uploadedFile.url,
          filename: file.originalname,
          mimeType: file.mimetype,
        }
      })
    )

    res.json({
      files: uploadedFiles,
    })
  } catch (error) {
    console.error('❌ Failed to upload files:', error)
    res.status(500).json({
      error: error.message,
      message: "Failed to upload files",
    })
  }
}
