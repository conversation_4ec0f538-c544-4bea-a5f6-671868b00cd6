import { ExecArgs } from "@medusajs/framework/types"
import {
  ContainerRegistrationKeys,
  ModuleRegistrationName,
} from "@medusajs/framework/utils"

const checkShippingOptions = async ({ container }: ExecArgs) => {
  console.log("🚢 Checking Shipping Options for Digital Products...")
  console.log("=" .repeat(60))
  
  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    
    // Check shipping options
    console.log("1️⃣ Checking Shipping Options...")
    try {
      const { data: shippingOptions } = await query.graph({
        entity: "shipping_option",
        fields: [
          "*",
          "type.*",
          "provider.*",
          "shipping_profile.*",
        ],
      })

      console.log(`✅ Found ${shippingOptions.length} shipping options`)
      
      for (const option of shippingOptions) {
        console.log(`   📦 ${option.name}`)
        console.log(`      - ID: ${option.id}`)
        console.log(`      - Provider: ${option.provider_id}`)
        console.log(`      - Type: ${option.type?.label || 'Unknown'}`)
        console.log(`      - Profile: ${option.shipping_profile?.name || 'Unknown'}`)
        console.log(`      - Enabled: ${option.enabled}`)
      }
    } catch (error) {
      console.log("❌ Error fetching shipping options:", error.message)
    }

    // Check fulfillment providers
    console.log("\n2️⃣ Checking Fulfillment Providers...")
    try {
      const fulfillmentModuleService = container.resolve(
        ModuleRegistrationName.FULFILLMENT
      )
      
      const providers = await fulfillmentModuleService.listFulfillmentProviders()
      console.log(`✅ Found ${providers.length} fulfillment providers`)
      
      for (const provider of providers) {
        console.log(`   🔧 ${provider.id}`)
        console.log(`      - Name: ${provider.name || 'Unknown'}`)
      }
    } catch (error) {
      console.log("❌ Error fetching fulfillment providers:", error.message)
    }

    // Check the specific order that has digital products
    console.log("\n3️⃣ Checking Order Details...")
    const orderId = "order_01JZMD5KYT3E3ZRJ98PFCCCDPA"
    
    try {
      const { data: orders } = await query.graph({
        entity: "order",
        fields: [
          "*",
          "items.*",
          "items.variant.*",
          "items.variant.digital_product.*",
          "fulfillments.*",
          "shipping_methods.*",
          "shipping_methods.shipping_option.*",
          "shipping_methods.shipping_option.provider.*",
        ],
        filters: {
          id: orderId,
        },
      })

      if (orders.length > 0) {
        const order = orders[0]
        console.log(`✅ Order ${order.id}`)
        console.log(`   - Status: ${order.status}`)
        console.log(`   - Fulfillments: ${order.fulfillments?.length || 0}`)
        console.log(`   - Shipping Methods: ${order.shipping_methods?.length || 0}`)
        
        if (order.shipping_methods) {
          for (const method of order.shipping_methods) {
            console.log(`     🚢 Shipping Method: ${method.name}`)
            console.log(`        - Option: ${method.shipping_option?.name}`)
            console.log(`        - Provider: ${method.shipping_option?.provider_id}`)
          }
        }
        
        if (order.items) {
          console.log(`   - Items: ${order.items.length}`)
          for (const item of order.items) {
            if (item.variant?.digital_product) {
              console.log(`     📱 Digital Item: ${item.title}`)
              console.log(`        - Variant: ${item.variant.title}`)
              console.log(`        - Digital Product: ${item.variant.digital_product.name}`)
            }
          }
        }
      } else {
        console.log("❌ Order not found")
      }
    } catch (error) {
      console.log("❌ Error fetching order details:", error.message)
    }

    console.log("\n" + "=" .repeat(60))
    console.log("📋 ANALYSIS")
    console.log("=" .repeat(60))
    console.log("🔍 If no fulfillments are created for digital orders:")
    console.log("   1. Check if shipping options use digital fulfillment provider")
    console.log("   2. Ensure digital products have correct shipping profile")
    console.log("   3. Verify shipping methods are applied to orders")
    console.log("   4. Check if fulfillment creation is triggered")

  } catch (error) {
    console.error("❌ CRITICAL ERROR:", error.message)
    console.error("Stack:", error.stack)
  }
}

export default checkShippingOptions
