// Simple test to call the manual fulfillment API
async function testManualFulfillment() {
  console.log('🧪 Testing Manual Digital Fulfillment API')
  console.log('⏰ Timestamp:', new Date().toISOString())

  try {
    // Replace with an actual order ID from your system
    const testOrderId = 'order_01K1NKY49D3JJRVAN3N88CWZR5' // Update this with a real order ID
    const apiUrl = `http://localhost:9000/admin/orders/${testOrderId}/fulfill-digital`

    console.log(`📞 Calling API: ${apiUrl}`)

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add authorization header if needed
        // 'Authorization': 'Bearer your-admin-token'
      },
    })

    const result = await response.json()

    console.log('📋 API Response:')
    console.log('  - Status:', response.status)
    console.log('  - Success:', result.success)
    console.log('  - Message:', result.message)
    console.log('  - Order ID:', result.order_id)
    console.log('  - Fulfillment ID:', result.fulfillment_id)
    console.log('  - Digital Products Count:', result.digital_products_count)

    if (result.success) {
      console.log('✅ Manual fulfillment completed successfully!')
      console.log('  - Digital Product Order ID:', result.digital_product_order_id)
      console.log('  - Workflow Result:', JSON.stringify(result.workflow_result, null, 2))
    } else {
      console.log('❌ Manual fulfillment failed')
      console.log('  - Error details:', JSON.stringify(result, null, 2))
    }

  } catch (error) {
    console.error('❌ CRITICAL ERROR IN MANUAL FULFILLMENT TEST')
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack Trace:', error.stack)
  }
}

testManualFulfillment()
