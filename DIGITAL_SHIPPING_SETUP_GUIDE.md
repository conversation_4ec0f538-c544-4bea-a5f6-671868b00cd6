# 🚢 Digital Shipping Options Setup Guide

## 🎯 **Overview**
Digital products need special shipping options because they are delivered instantly via download links, not physical shipping.

## 📋 **Prerequisites**
- ✅ Digital fulfillment provider configured (`digital-fulfillment`)
- ✅ Digital products created and linked to variants
- ✅ MedusaJS backend running

---

## 🛠️ **Method 1: Admin Panel Setup (Recommended)**

### **Step 1: Create Digital Shipping Profile**
1. **Navigate**: Admin Panel → Settings → Shipping Profiles
2. **Click**: "Create Shipping Profile"
3. **Configure**:
   ```
   Name: Digital Products
   Type: default (or custom)
   ```
4. **Add Products**: Select all your digital products
5. **Save**

### **Step 2: Create Service Zone (if needed)**
1. **Navigate**: Settings → Locations → [Your Location]
2. **Go to**: Fulfillment Sets tab
3. **Create/Edit**: Fulfillment Set
4. **Add Service Zone**:
   ```
   Name: Global Digital Delivery
   Geo Zones: Add countries (or worldwide)
   ```

### **Step 3: Create Digital Shipping Option**
1. **Navigate**: Settings → Shipping Options
2. **Click**: "Create Shipping Option"
3. **Configure**:
   ```
   Name: Digital Delivery
   Provider: digital-fulfillment
   Shipping Profile: Digital Products
   Service Zone: Global Digital Delivery
   Price Type: Flat Rate
   Amount: 0 (free)
   Currency: USD (or your currency)
   ```
4. **Save**

---

## 🤖 **Method 2: Automated Script Setup**

### **Run the Setup Script**:
```bash
cd backend
npx medusa exec ./src/scripts/setup-digital-shipping.ts
```

### **What the Script Does**:
1. ✅ Creates "Digital Products" shipping profile
2. ✅ Creates digital shipping option with `digital-fulfillment` provider
3. ✅ Sets up free pricing (0 cost)
4. ✅ Configures proper service zones
5. ✅ Links everything together

---

## 🔧 **Method 3: Manual API Setup**

### **Create Shipping Profile**:
```typescript
const shippingProfile = await fulfillmentModuleService.createShippingProfiles({
  name: "Digital Products",
  type: "Digital",
})
```

### **Create Shipping Option**:
```typescript
const digitalShippingOption = await fulfillmentModuleService.createShippingOptions({
  name: "Digital Product Delivery",
  price_type: "flat",
  service_zone_id: serviceZone.id, // Your service zone
  shipping_profile_id: shippingProfile.id,
  provider_id: "digital-fulfillment",
  type_id: shippingOptionType.id,
  data: {
    delivery_method: "digital",
    instant_delivery: true,
  },
  prices: [
    {
      currency_code: "USD",
      amount: 0, // Free digital delivery
    },
  ],
})
```

---

## 🔍 **Verification Steps**

### **1. Check Shipping Options**:
```bash
npx medusa exec ./src/scripts/check-shipping-options.ts
```

### **2. Expected Output**:
```
🎯 DIGITAL FULFILLMENT OPTIONS: 1
1. Digital Product Delivery
   - Has Service Zone: YES
   - Has Shipping Profile: YES
```

### **3. Test Order Flow**:
1. Add digital product to cart
2. Go to checkout
3. **Verify**: "Digital Delivery" appears as shipping option
4. **Verify**: Price is 0 (free)
5. Complete order
6. **Verify**: Order gets fulfilled automatically

---

## 🚨 **Common Issues & Solutions**

### **Issue 1: No Shipping Options Appear**
**Cause**: Shipping profile not linked to digital products
**Solution**: 
1. Go to Products → [Digital Product] → Shipping Profile
2. Select "Digital Products" profile

### **Issue 2: "service_zone undefined" Error**
**Cause**: Shipping option not linked to service zone
**Solution**:
1. Create service zone first
2. Link shipping option to service zone

### **Issue 3: Orders Stay "Pending"**
**Cause**: No fulfillment created
**Solution**:
1. Check if digital shipping option was selected during checkout
2. Verify `digital-order-fulfillment.ts` subscriber is working
3. Check server logs for errors

### **Issue 4: Multiple Fulfillment Providers**
**Cause**: Conflicting providers (manual vs digital)
**Solution**:
1. Use only `digital-fulfillment` for digital products
2. Use `manual` for physical products

---

## 📊 **Database Structure**

### **Key Tables**:
```sql
-- Shipping profiles for digital products
shipping_profile (name: "Digital Products")

-- Shipping options using digital provider
shipping_option (provider_id: "digital-fulfillment")

-- Service zones for delivery areas
service_zone (name: "Global Digital Delivery")

-- Pricing (free for digital)
shipping_option_price (amount: 0)
```

---

## 🎯 **Best Practices**

### **1. Naming Convention**:
- **Profile**: "Digital Products"
- **Option**: "Digital Delivery" or "Instant Download"
- **Zone**: "Global Digital Delivery"

### **2. Pricing**:
- Always set digital delivery to **0 cost**
- Support multiple currencies if needed

### **3. Service Zones**:
- Create **global zone** for worldwide digital delivery
- No geographic restrictions for digital products

### **4. Testing**:
- Test complete order flow after setup
- Verify automatic fulfillment works
- Check email notifications are sent

---

## 🔄 **Integration with Your System**

### **Your Current Setup**:
```typescript
// Provider: digital-fulfillment (✅ Already configured)
// Subscriber: digital-order-fulfillment.ts (✅ Already created)
// Workflow: fulfill-digital-order (✅ Already implemented)
```

### **Missing Piece**:
```typescript
// Shipping Options: ❌ Need to be created
// This is why orders stay "pending"
```

---

## 🚀 **Quick Start Commands**

### **1. Setup Everything**:
```bash
cd backend
npx medusa exec ./src/scripts/setup-digital-shipping.ts
```

### **2. Verify Setup**:
```bash
npx medusa exec ./src/scripts/check-shipping-options.ts
```

### **3. Test with Existing Order**:
```bash
npx medusa exec ./src/scripts/manual-fulfill-order-18.ts
```

---

## ✅ **Success Indicators**

After setup, you should see:
- ✅ Digital shipping option in admin panel
- ✅ "Digital Delivery" appears in checkout
- ✅ Orders with digital products get fulfilled automatically
- ✅ Customers receive download links via email
- ✅ Order status changes from "pending" to "fulfilled"

---

## 📞 **Need Help?**

If you encounter issues:
1. Check server logs: `tail -f logs/medusa.log`
2. Verify provider registration in `medusa-config.ts`
3. Test with a new order (not existing ones)
4. Check that digital products are linked to the correct shipping profile
