import { ExecArgs } from "@medusajs/framework/types"
import {
  ContainerRegistrationKeys,
  ModuleRegistrationName,
} from "@medusajs/framework/utils"

const testDigitalFulfillment = async ({ container }: ExecArgs) => {
  console.log("🧪 Testing Digital Fulfillment Setup...")
  console.log("=" .repeat(60))
  
  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    
    // 1. Check for fulfillment providers
    console.log("1️⃣ Checking Fulfillment Providers...")
    try {
      const fulfillmentModuleService = container.resolve(
        ModuleRegistrationName.FULFILLMENT
      )
      console.log("✅ Fulfillment module service resolved")
    } catch (error) {
      console.log("❌ Fulfillment module service not available:", error.message)
    }

    // 2. Check for digital products
    console.log("\n2️⃣ Checking Digital Products...")
    try {
      const { data: products } = await query.graph({
        entity: "product",
        fields: [
          "*",
          "variants.*",
          "variants.digital_product.*",
        ],
        filters: {},
        pagination: { take: 5 },
      })

      const digitalProducts = products.filter((product: any) => 
        product.variants?.some((variant: any) => variant.digital_product)
      )

      console.log(`✅ Found ${digitalProducts.length} products with digital variants`)
      digitalProducts.forEach((product: any, index: number) => {
        console.log(`   ${index + 1}. ${product.title}`)
        product.variants.forEach((variant: any) => {
          if (variant.digital_product) {
            console.log(`      - Variant: ${variant.title} (Digital: ${variant.digital_product.name})`)
          }
        })
      })

      if (digitalProducts.length === 0) {
        console.log("⚠️ No digital products found. Create some digital products first.")
      }
    } catch (error) {
      console.log("❌ Error checking digital products:", error.message)
    }

    // 3. Check for recent orders with digital products
    console.log("\n3️⃣ Checking Recent Orders with Digital Products...")
    try {
      const { data: orders } = await query.graph({
        entity: "order",
        fields: [
          "*",
          "items.*",
          "items.variant.*",
          "items.variant.digital_product.*",
          "fulfillments.*",
        ],
        filters: {},
        pagination: { take: 10 },
        options: {
          orderBy: { created_at: "DESC" },
        },
      })

      const digitalOrders = orders.filter((order: any) =>
        order.items?.some((item: any) => item.variant?.digital_product)
      )

      console.log(`✅ Found ${digitalOrders.length} orders with digital products`)
      digitalOrders.forEach((order: any, index: number) => {
        console.log(`   ${index + 1}. Order ${order.id} - Status: ${order.status}`)
        console.log(`      - Email: ${order.email}`)
        console.log(`      - Fulfillments: ${order.fulfillments?.length || 0}`)
        
        const digitalItems = order.items.filter((item: any) => item.variant?.digital_product)
        console.log(`      - Digital Items: ${digitalItems.length}`)
        digitalItems.forEach((item: any) => {
          console.log(`        * ${item.title} (${item.variant.digital_product.name})`)
        })
      })

      if (digitalOrders.length === 0) {
        console.log("⚠️ No orders with digital products found.")
      }
    } catch (error) {
      console.log("❌ Error checking orders:", error.message)
    }

    // 4. Check shipping profiles
    console.log("\n4️⃣ Checking Shipping Profiles...")
    try {
      const { data: shippingProfiles } = await query.graph({
        entity: "shipping_profile",
        fields: ["*", "shipping_options.*"],
        filters: {},
      })

      console.log(`✅ Found ${shippingProfiles.length} shipping profiles`)
      shippingProfiles.forEach((profile: any, index: number) => {
        console.log(`   ${index + 1}. ${profile.name} (Type: ${profile.type})`)
        if (profile.shipping_options) {
          profile.shipping_options.forEach((option: any) => {
            console.log(`      - Option: ${option.name} (Provider: ${option.provider_id})`)
          })
        }
      })

      const digitalProfile = shippingProfiles.find((profile: any) => 
        profile.name?.toLowerCase().includes('digital') || 
        profile.type?.toLowerCase().includes('digital')
      )

      if (digitalProfile) {
        console.log(`✅ Digital shipping profile found: ${digitalProfile.name}`)
      } else {
        console.log("⚠️ No digital shipping profile found. Create one in admin panel.")
      }
    } catch (error) {
      console.log("❌ Error checking shipping profiles:", error.message)
    }

    // 5. Summary and recommendations
    console.log("\n" + "=" .repeat(60))
    console.log("📋 SETUP SUMMARY")
    console.log("=" .repeat(60))
    
    console.log("\n✅ Components Installed:")
    console.log("   - Auto-fulfillment workflow")
    console.log("   - Auto-fulfillment subscriber")
    console.log("   - Manual fulfillment API")
    console.log("   - Digital fulfillment providers")
    
    console.log("\n📝 Next Steps:")
    console.log("   1. Create digital products if none exist")
    console.log("   2. Set up 'Digital Products' shipping profile")
    console.log("   3. Configure shipping options with digital fulfillment provider")
    console.log("   4. Test with a real order")
    console.log("   5. Monitor server logs for fulfillment events")
    
    console.log("\n🔧 Manual Fulfillment API:")
    console.log("   POST /admin/orders/{order_id}/fulfill-digital")
    
    console.log("\n📊 Monitor these log messages:")
    console.log("   - 🔔 AUTO-FULFILL DIGITAL ORDERS - Fulfillment created event received")
    console.log("   - 🚀 DIGITAL FULFILLMENT DETECTED - STARTING AUTO-FULFILLMENT WORKFLOW")
    console.log("   - ✅ AUTO-FULFILLMENT WORKFLOW COMPLETED")

  } catch (error) {
    console.error("❌ Error during digital fulfillment test:", error)
  }

  console.log("\n🧪 Digital Fulfillment Test Completed!")
}

export default testDigitalFulfillment
