import type {
  SubscriberConfig,
  SubscriberArgs,
} from "@medusajs/framework"
import {
  ContainerRegistrationKeys,
  ModuleRegistrationName,
} from "@medusajs/framework/utils"
import { createOrderFulfillmentWorkflow } from "@medusajs/medusa/core-flows"
import fulfillDigitalOrderWorkflow from "../workflows/fulfill-digital-order"

// Subscriber that handles digital product fulfillment when an order is placed
export default async function handleDigitalOrderFulfillment({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  console.log('🔔 DIGITAL ORDER FULFILLMENT - Order placed event received')
  console.log('📋 Event Data:', JSON.stringify(data, null, 2))
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('🎯 Order ID:', data.id)

  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    const notificationModuleService = container.resolve(
      ModuleRegistrationName.NOTIFICATION
    )

    console.log('🔍 Fetching order details for digital fulfillment...')

    // Get order details with linked digital products
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "items.*",
        "items.product.*",
        "items.variant.*",
        "items.variant.digital_product.*",
        "items.variant.digital_product.medias.*",
        "billing_address.*",
        "shipping_address.*",
        "customer.*",
      ],
      filters: {
        id: data.id,
      },
    })

    if (!orders || orders.length === 0) {
      console.log('❌ DIGITAL FULFILLMENT - Order not found:', data.id)
      return
    }

    const order = orders[0]
    console.log('📦 DIGITAL FULFILLMENT - Processing order details:')
    console.log('  - Order ID:', order.id)
    console.log('  - Customer Email:', order.email)
    console.log('  - Customer ID:', order.customer_id)
    console.log('  - Order Status:', order.status)
    console.log('  - Total Amount:', order.total, order.currency_code)
    console.log('  - Items Count:', order.items?.length || 0)
    console.log('  - Customer Name:', order.customer?.first_name, order.customer?.last_name)
    console.log('  - Billing Address:', order.billing_address?.first_name, order.billing_address?.last_name)

    console.log('🔍 ANALYZING ORDER ITEMS FOR DIGITAL PRODUCTS:')

    // Check for digital products in order items
    const digitalProducts: any[] = []

    if (order.items) {
      order.items.forEach((item: any, index: number) => {
        console.log(`  📋 Item ${index + 1}:`)
        console.log('    - Product ID:', item?.product?.id)
        console.log('    - Product Title:', item?.product?.title)
        console.log('    - Variant ID:', item?.variant?.id)
        console.log('    - Quantity:', item?.quantity)
        console.log('    - Has Digital Product Link:', !!item?.variant?.digital_product)

        if (item?.variant?.digital_product) {
          console.log('    - Digital Product ID:', item.variant.digital_product.id)
          console.log('    - Digital Product Name:', item.variant.digital_product.name)
          console.log('    - Digital Product Medias:', item.variant.digital_product.medias?.length || 0)
        }

        // Check if this variant has a linked digital product
        const digitalProduct = item?.variant?.digital_product
        if (digitalProduct && digitalProduct.medias && digitalProduct.medias.length > 0) {
          try {
            console.log('    ✅ Processing digital product:', digitalProduct.name)
            console.log('    📁 Digital medias found:', digitalProduct.medias.length)

            // Log each media for debugging
            digitalProduct.medias.forEach((media: any, mediaIndex: number) => {
              console.log(`      Media ${mediaIndex + 1}:`)
              console.log('        - Type:', media.type)
              console.log('        - URL:', media.url || 'None')
              console.log('        - File ID:', media.fileId || 'None')
              console.log('        - MIME Type:', media.mimeType || 'None')
            })

            digitalProducts.push({
              id: digitalProduct.id,
              name: digitalProduct.name,
              medias: digitalProduct.medias,
              product_title: item.product?.title,
              variant_title: item.variant?.title,
            })
            console.log('    � Digital product added to fulfillment list')
          } catch (error) {
            console.error('    ❌ Failed to process digital product:', digitalProduct.id, error)
          }
        } else {
          console.log('    ℹ️ No digital product linked to this variant or no medias available')
        }
      })
    }

    console.log('📊 DIGITAL PRODUCTS SUMMARY:')
    console.log('  - Total digital products found:', digitalProducts.length)
    console.log('  - Digital products:', digitalProducts.map(p => ({ id: p.id, name: p.name })))

    if (digitalProducts.length === 0) {
      console.log('ℹ️ No digital products found in order - skipping digital fulfillment')
      return
    }

    console.log('📧 PREPARING DIGITAL PRODUCT NOTIFICATION')
    console.log('  - Recipient email:', order.email)
    console.log('  - Customer name from billing:', `${order.billing_address?.first_name || ''} ${order.billing_address?.last_name || ''}`.trim())
    console.log('  - Products to include:', digitalProducts.length)

    try {
      // Send notification to customer with digital content
      await notificationModuleService.createNotifications({
        to: order.email || '',
        template: "digital-order-template",
        channel: "email",
        data: {
          order_id: order.id,
          customer_name: `${order.billing_address?.first_name || ''} ${order.billing_address?.last_name || ''}`.trim(),
          products: digitalProducts,
        },
      })

      console.log('✅ DIGITAL NOTIFICATION SENT SUCCESSFULLY')
      console.log('  - Template: digital-order-template')
      console.log('  - Channel: email')
      console.log('  - Recipient:', order.email)
    } catch (notificationError) {
      console.error('❌ FAILED TO SEND DIGITAL NOTIFICATION')
      console.error('  - Error:', notificationError)
      console.error('  - Order ID:', order.id)
      console.error('  - Email:', order.email)
      throw notificationError
    }

    console.log('✅ DIGITAL ORDER FULFILLMENT COMPLETED')
    console.log('  - Order ID:', order.id)
    console.log('  - Digital products processed:', digitalProducts.length)

    // Create actual fulfillment for digital products using MedusaJS core workflow
    console.log('🚀 CREATING DIGITAL FULFILLMENT')

    try {
      // Use the core MedusaJS workflow to create fulfillment
      const digitalItems = order.items.filter((item: any) => item.variant?.digital_product)

      const { result: fulfillmentResult } = await createOrderFulfillmentWorkflow(container).run({
        input: {
          order_id: order.id,
          items: digitalItems.map((item: any) => ({
            id: item.id,
            quantity: item.quantity,
          })),
        },
      })

      console.log('✅ DIGITAL FULFILLMENT CREATED')
      console.log('  - Fulfillment ID:', fulfillmentResult?.id)

      // Now run the digital order workflow with the real fulfillment ID
      const { result } = await fulfillDigitalOrderWorkflow(container).run({
        input: {
          order_id: order.id,
          fulfillment_id: fulfillmentResult?.id,
        },
      })

      console.log('✅ FULFILLMENT WORKFLOW COMPLETED')
      console.log('  - Order Status Updated:', result?.order?.status)
      console.log('  - Digital Product Order ID:', result?.digital_product_order?.id)
    } catch (fulfillmentError) {
      console.error('❌ Error in fulfillment workflow:', fulfillmentError.message)
      console.error('  - Stack:', fulfillmentError.stack)
      // Don't throw - the main digital delivery was successful
    }
  } catch (error) {
    console.error('❌ CRITICAL ERROR IN DIGITAL ORDER FULFILLMENT')
    console.error('  - Order ID:', data.id)
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack Trace:', error.stack)
    console.error('  - Timestamp:', new Date().toISOString())
    console.error('  - Note: Error logged for manual investigation')

    // Don't throw error to prevent order completion failure
    // Digital fulfillment can be handled manually if needed
  }

  console.log('🏁 DIGITAL ORDER FULFILLMENT HANDLER COMPLETED')
  console.log('=' .repeat(80))
}

export const config: SubscriberConfig = {
  event: "order.placed",
}
