import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Get the product module service
    const productModuleService = req.scope.resolve("product")
    
    // Get the first product
    const products = await productModuleService.list({}, { take: 1 })
    
    if (products.length === 0) {
      return res.status(404).json({ error: "No products found" })
    }

    const product = products[0]
    
    // Add some test metadata
    const updatedProduct = await productModuleService.update(product.id, {
      metadata: {
        difficulty_level: "beginner",
        video_url: "https://youtube.com/watch?v=test",
        reading_time_minutes: "15",
        is_featured: "true",
        topics: JSON.stringify(["hebrew", "learning"]),
        test_field: "This is a test metadata field"
      }
    })

    console.log("✅ Added metadata to product:", product.id)

    res.json({
      message: "<PERSON><PERSON><PERSON> added successfully",
      product_id: product.id,
      metadata: updatedProduct.metadata
    })

  } catch (error) {
    console.error("❌ Failed to add metadata:", error)
    res.status(500).json({
      error: "Failed to add metadata",
      details: error.message
    })
  }
}
