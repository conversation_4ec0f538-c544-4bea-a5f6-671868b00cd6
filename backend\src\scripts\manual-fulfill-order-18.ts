import { ExecArgs } from "@medusajs/framework/types"
import { ContainerRegistrationKeys, ModuleRegistrationName } from "@medusajs/framework/utils"
import { createOrderFulfillmentWorkflow } from "@medusajs/medusa/core-flows"
import fulfillDigitalOrderWorkflow from "../workflows/fulfill-digital-order"

export default async function manualFulfillOrder18({ container }: ExecArgs) {
  console.log('🔧 Manual Digital Fulfillment for Order #18')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('=' .repeat(60))

  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)

    // Find order #18 (display_id = 18)
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "items.*",
        "items.variant.*",
        "items.variant.digital_product.*",
        "items.variant.digital_product.medias.*",
        "fulfillments.*",
        "customer.*",
      ],
      filters: {
        display_id: 18,
      },
    })

    if (!orders || orders.length === 0) {
      console.log('❌ Order #18 not found')
      return
    }

    const order = orders[0]
    console.log('📦 Found Order #18:')
    console.log('  - ID:', order.id)
    console.log('  - Status:', order.status)
    console.log('  - Customer:', order.customer?.email || order.email)
    console.log('  - Items:', order.items?.length || 0)
    console.log('  - Fulfillments:', order.fulfillments?.length || 0)

    // Check for digital products
    const digitalItems = order.items?.filter((item: any) => item.variant?.digital_product) || []
    console.log('  - Digital Items:', digitalItems.length)

    if (digitalItems.length === 0) {
      console.log('❌ No digital products found in order #18')
      return
    }

    digitalItems.forEach((item: any, index: number) => {
      console.log(`    ${index + 1}. ${item.title}`)
      console.log(`       - Digital Product: ${item.variant?.digital_product?.name}`)
      console.log(`       - Medias: ${item.variant?.digital_product?.medias?.length || 0}`)
    })

    // Check if fulfillment already exists
    if (order.fulfillments && order.fulfillments.length > 0) {
      console.log('⚠️ Order already has fulfillments:')
      order.fulfillments.forEach((fulfillment: any, index: number) => {
        console.log(`  ${index + 1}. ${fulfillment.id} - Provider: ${fulfillment.provider_id}`)
      })
      console.log('Proceeding anyway to test workflow...')
    }

    // Create fulfillment for digital products using MedusaJS core workflow
    console.log('\n🚀 Creating Digital Fulfillment...')

    const { result: fulfillmentResult } = await createOrderFulfillmentWorkflow(container).run({
      input: {
        order_id: order.id,
        items: digitalItems.map((item: any) => ({
          id: item.id,
          quantity: item.quantity,
        })),
      },
    })

    console.log('✅ Digital Fulfillment Created:')
    console.log('  - Fulfillment ID:', fulfillmentResult?.id)
    console.log('  - Provider:', fulfillmentResult?.provider_id)

    // Run the digital order fulfillment workflow
    console.log('\n🚀 Running Digital Order Fulfillment Workflow...')
    
    const { result } = await fulfillDigitalOrderWorkflow(container).run({
      input: {
        order_id: order.id,
        fulfillment_id: fulfillmentResult?.id,
      },
    })

    console.log('✅ Workflow Completed Successfully:')
    console.log('  - Order ID:', result?.order?.id)
    console.log('  - Digital Product Order ID:', result?.digital_product_order?.id)
    console.log('  - Digital Products Count:', result?.digital_products?.length || 0)
    console.log('  - Success:', result?.success)
    console.log('  - Message:', result?.message)

    // Check final order status
    console.log('\n📋 Checking Final Order Status...')
    const { data: updatedOrders } = await query.graph({
      entity: "order",
      fields: ["*", "fulfillments.*"],
      filters: { id: order.id },
    })

    if (updatedOrders && updatedOrders.length > 0) {
      const updatedOrder = updatedOrders[0]
      console.log('📦 Updated Order Status:')
      console.log('  - Status:', updatedOrder.status)
      console.log('  - Fulfillments:', updatedOrder.fulfillments?.length || 0)
      
      if (updatedOrder.fulfillments) {
        updatedOrder.fulfillments.forEach((f: any, index: number) => {
          console.log(`    ${index + 1}. ${f.id}`)
          console.log(`       - Provider: ${f.provider_id}`)
          console.log(`       - Shipped: ${f.shipped_at || 'Not shipped'}`)
        })
      }
    }

  } catch (error) {
    console.error('❌ Error in manual fulfillment:', error.message)
    console.error('Stack:', error.stack)
  }

  console.log('\n✅ Manual fulfillment completed')
  console.log('=' .repeat(60))
}
