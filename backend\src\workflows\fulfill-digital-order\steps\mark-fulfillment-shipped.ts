import {
  createStep,
  StepResponse,
} from "@medusajs/framework/workflows-sdk"
import {
  ModuleRegistrationName,
} from "@medusajs/framework/utils"

export type MarkFulfillmentShippedStepInput = {
  fulfillment_id: string
  order_id: string
}

export const markFulfillmentShippedStep = createStep(
  "mark-fulfillment-shipped",
  async (
    { fulfillment_id, order_id }: MarkFulfillmentShippedStepInput,
    { container }
  ) => {
    console.log('📦 Marking fulfillment as shipped for digital order')
    console.log('  - Fulfillment ID:', fulfillment_id)
    console.log('  - Order ID:', order_id)

    try {
      const fulfillmentModuleService = container.resolve(
        ModuleRegistrationName.FULFILLMENT
      )

      // Mark fulfillment as shipped (delivered for digital products)
      const updatedFulfillment = await fulfillmentModuleService.updateFulfillments(
        fulfillment_id,
        {
          shipped_at: new Date(),
          data: {
            tracking_number: `DIGITAL_${order_id}_${Date.now()}`,
            tracking_url: null,
            delivery_method: "digital",
            delivered_at: new Date().toISOString(),
            status: "delivered",
          },
        }
      )

      console.log('✅ Fulfillment marked as shipped/delivered')
      console.log('  - Fulfillment ID:', updatedFulfillment.id)
      console.log('  - Shipped At:', updatedFulfillment.shipped_at)

      return new StepResponse({
        fulfillment: updatedFulfillment,
      })
    } catch (error) {
      console.error('❌ Error marking fulfillment as shipped:', error)
      
      // Return success even if this fails to prevent workflow failure
      // The main digital delivery should still work
      return new StepResponse({
        fulfillment: { id: fulfillment_id, error: error.message },
      })
    }
  }
)

export default markFulfillmentShippedStep
