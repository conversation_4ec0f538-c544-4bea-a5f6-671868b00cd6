import { ExecArgs } from "@medusajs/framework/types"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

export default async function checkOrder18({ container }: ExecArgs) {
  console.log('🔍 Checking Order #18 Status')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('=' .repeat(60))

  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)

    // Get recent orders to find order #18 or similar
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "items.*",
        "items.variant.*",
        "items.variant.digital_product.*",
        "items.variant.digital_product.medias.*",
        "fulfillments.*",
        "customer.*",
      ],
      filters: {},
      pagination: { take: 20 },
      options: {
        orderBy: { created_at: "DESC" },
      },
    })

    console.log(`📋 Found ${orders.length} recent orders`)

    // Look for orders with digital products
    const digitalOrders = orders.filter((order: any) =>
      order.items?.some((item: any) => item.variant?.digital_product)
    )

    console.log(`🎯 Found ${digitalOrders.length} orders with digital products`)

    digitalOrders.forEach((order: any, index: number) => {
      console.log(`\n📦 ORDER ${index + 1}: ${order.id}`)
      console.log(`  - Display ID: ${order.display_id}`)
      console.log(`  - Status: ${order.status}`)
      console.log(`  - Customer: ${order.customer?.email || order.email}`)
      console.log(`  - Created: ${order.created_at}`)
      console.log(`  - Total: ${order.total} ${order.currency_code}`)
      console.log(`  - Items: ${order.items?.length || 0}`)
      console.log(`  - Fulfillments: ${order.fulfillments?.length || 0}`)

      // Check digital products
      const digitalItems = order.items?.filter((item: any) => item.variant?.digital_product) || []
      console.log(`  - Digital Items: ${digitalItems.length}`)
      
      digitalItems.forEach((item: any, itemIndex: number) => {
        console.log(`    ${itemIndex + 1}. ${item.title}`)
        console.log(`       - Variant: ${item.variant?.title || 'Default'}`)
        console.log(`       - Digital Product: ${item.variant?.digital_product?.name}`)
        console.log(`       - Medias: ${item.variant?.digital_product?.medias?.length || 0}`)
      })

      // Check fulfillments
      if (order.fulfillments && order.fulfillments.length > 0) {
        console.log(`  📦 FULFILLMENTS:`)
        order.fulfillments.forEach((fulfillment: any, fIndex: number) => {
          console.log(`    ${fIndex + 1}. ID: ${fulfillment.id}`)
          console.log(`       - Provider: ${fulfillment.provider_id}`)
          console.log(`       - Created: ${fulfillment.created_at}`)
          console.log(`       - Shipped: ${fulfillment.shipped_at || 'Not shipped'}`)
          console.log(`       - Items: ${fulfillment.items?.length || 0}`)
        })
      } else {
        console.log(`  ⚠️ NO FULFILLMENTS - THIS IS THE PROBLEM!`)
      }
    })

    if (digitalOrders.length === 0) {
      console.log('\n⚠️ No orders with digital products found in recent orders')
      console.log('   This might mean:')
      console.log('   1. No digital products have been ordered recently')
      console.log('   2. Digital product links are not set up correctly')
      console.log('   3. The query is not finding the relationships')
    }

  } catch (error) {
    console.error('❌ Error checking orders:', error.message)
    console.error('Stack:', error.stack)
  }

  console.log('\n✅ Order check completed')
  console.log('=' .repeat(60))
}
