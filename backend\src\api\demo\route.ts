import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const html = `
<!DOCTYPE html>
<html lang="en" dir="ltr" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hebrew Book Store - Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: ltr;
            transition: direction 0.3s ease;
        }

        body.rtl {
            direction: rtl;
        }

        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 5px;
            display: flex;
            gap: 5px;
        }

        .lang-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: transparent;
            color: #666;
        }

        .lang-btn.active {
            background: #4299e1;
            color: white;
        }

        .lang-btn:hover {
            background: #e2e8f0;
        }

        .lang-btn.active:hover {
            background: #3182ce;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .api-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .api-section h2 {
            color: #333;
            margin-top: 0;
        }
        .api-url {
            background: #2d3748;
            color: #68d391;
            padding: 10px 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        .test-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #3182ce;
        }
        .response {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
            display: none;
        }
        .chapter-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: white;
        }
        .chapter-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }
        .chapter-meta {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .chapter-preview {
            color: #4a5568;
            line-height: 1.6;
        }
        .price-tag {
            background: #48bb78;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            display: inline-block;
            margin-top: 10px;
        }
        .free-tag {
            background: #ed8936;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Language Switcher -->
        <div class="language-switcher">
            <button class="lang-btn active" onclick="switchLanguage('en')" data-lang="en">🇺🇸 EN</button>
            <button class="lang-btn" onclick="switchLanguage('he')" data-lang="he">🇮🇱 עב</button>
            <button class="lang-btn" onclick="switchLanguage('ru')" data-lang="ru">🇷🇺 РУ</button>
        </div>

        <div class="header">
            <h1 data-en="📚 Hebrew Book Store" data-he="📚 חנות ספרי עברית" data-ru="📚 Магазин книг на иврите">📚 Hebrew Book Store</h1>
            <p data-en="Advanced Hebrew Learning Platform" data-he="פלטפורמת למידת עברית מתקדמת" data-ru="Продвинутая платформа изучения иврита">Advanced Hebrew Learning Platform</p>
            <p data-en="Interactive e-commerce platform for Hebrew language learning" data-he="פלטפורמת מסחר אלקטרוני אינטראקטיבית ללימוד עברית" data-ru="Интерактивная платформа электронной коммерции для изучения иврита">Interactive e-commerce platform for Hebrew language learning</p>
        </div>
        
        <div class="content">
            <div class="api-section">
                <h2 data-en="🔧 API Testing" data-he="🔧 בדיקת API" data-ru="🔧 Тестирование API">🔧 API Testing</h2>
                <p data-en="Test our platform APIs:" data-he="בדיקת ה-API שלנו:" data-ru="Тестируйте наши API:">Test our platform APIs:</p>

                <h3 data-en="1. Chapters List" data-he="1. רשימת פרקים" data-ru="1. Список глав">1. Chapters List</h3>
                <div class="api-url">GET /public/chapters</div>
                <button class="test-button" onclick="testAPI('/public/chapters', 'chapters-response')" data-en="Test" data-he="בדיקה" data-ru="Тест">Test</button>
                <button class="test-button" onclick="testAPI('/public/chapters?difficulty_level=beginner', 'chapters-response')" data-en="Beginners Only" data-he="מתחילים בלבד" data-ru="Только для начинающих">Beginners Only</button>
                <button class="test-button" onclick="testAPI('/public/chapters?is_free=true', 'chapters-response')" data-en="Free Only" data-he="חינם בלבד" data-ru="Только бесплатные">Free Only</button>
                <div id="chapters-response" class="response"></div>

                <h3 data-en="2. Specific Chapter" data-he="2. פרק ספציפי" data-ru="2. Конкретная глава">2. Specific Chapter</h3>
                <div class="api-url">GET /public/chapters/{id}</div>
                <button class="test-button" onclick="testAPI('/public/chapters/chapter_1', 'chapter-response')" data-en="Chapter 1 (Free)" data-he="פרק 1 (חינם)" data-ru="Глава 1 (Бесплатно)">Chapter 1 (Free)</button>
                <button class="test-button" onclick="testAPI('/public/chapters/chapter_2', 'chapter-response')" data-en="Chapter 2 (Paid)" data-he="פרק 2 (בתשלום)" data-ru="Глава 2 (Платно)">Chapter 2 (Paid)</button>
                <button class="test-button" onclick="testAPI('/public/chapters/chapter_3', 'chapter-response')" data-en="Chapter 3 (Paid)" data-he="פרק 3 (בתשלום)" data-ru="Глава 3 (Платно)">Chapter 3 (Paid)</button>
                <div id="chapter-response" class="response"></div>

                <h3>3. תוכניות מנוי (Subscription Plans)</h3>
                <div class="api-url">GET /public/subscriptions/plans</div>
                <button class="test-button" onclick="testAPI('/public/subscriptions/plans', 'plans-response')">תוכניות מנוי</button>
                <div id="plans-response" class="response"></div>

                <h3>4. חיפוש (Search)</h3>
                <div class="api-url">GET /public/search?q={query}</div>
                <button class="test-button" onclick="testAPI('/public/search?q=משפחה', 'search-response')">חיפוש: משפחה</button>
                <button class="test-button" onclick="testAPI('/public/search?q=אלפבית', 'search-response')">חיפוש: אלפבית</button>
                <button class="test-button" onclick="testAPI('/public/search?q=צבעים&type=vocabulary', 'search-response')">חיפוש: צבעים (אוצר מילים)</button>
                <div id="search-response" class="response"></div>

                <h3>5. סטטיסטיקות (Statistics)</h3>
                <div class="api-url">GET /public/stats</div>
                <button class="test-button" onclick="testAPI('/public/stats', 'stats-response')">סטטיסטיקות פלטפורמה</button>
                <div id="stats-response" class="response"></div>

                <h3>6. בדיקת מערכת (System Check)</h3>
                <div class="api-url">GET /test/chapters</div>
                <button class="test-button" onclick="testAPI('/test/chapters', 'system-response')">בדיקת מערכת</button>
                <div id="system-response" class="response"></div>
            </div>
            
            <div class="api-section">
                <h2 data-en="🔍 Interactive Search" data-he="🔍 חיפוש אינטראקטיבי" data-ru="🔍 Интерактивный поиск">🔍 Interactive Search</h2>
                <p data-en="Search content in the platform:" data-he="חפשו תוכן בפלטפורמה:" data-ru="Поиск контента на платформе:">Search content in the platform:</p>
                <div style="margin-bottom: 20px;">
                    <input type="text" id="search-input"
                           data-placeholder-en="Type here to search..."
                           data-placeholder-he="הקלידו כאן לחיפוש..."
                           data-placeholder-ru="Введите здесь для поиска..."
                           placeholder="Type here to search..."
                           style="padding: 10px; width: 300px; border: 1px solid #ccc; border-radius: 5px; margin-left: 10px;">
                    <button class="test-button" onclick="performSearch()" data-en="Search" data-he="חיפוש" data-ru="Поиск">Search</button>
                </div>
                <div id="search-results" style="display: none;"></div>
            </div>

            <div class="api-section">
                <h2 data-en="📊 Platform Statistics" data-he="📊 סטטיסטיקות פלטפורמה" data-ru="📊 Статистика платформы">📊 Platform Statistics</h2>
                <div id="platform-stats" class="loading" data-en="Loading statistics..." data-he="טוען סטטיסטיקות..." data-ru="Загрузка статистики...">Loading statistics...</div>
            </div>

            <div class="api-section">
                <h2 data-en="💳 Subscription Plans" data-he="💳 תוכניות מנוי" data-ru="💳 Планы подписки">💳 Subscription Plans</h2>
                <div id="subscription-plans" class="loading" data-en="Loading subscription plans..." data-he="טוען תוכניות מנוי..." data-ru="Загрузка планов подписки...">Loading subscription plans...</div>
            </div>

            <div class="api-section">
                <h2 data-en="📖 Chapters Display" data-he="📖 תצוגת פרקים" data-ru="📖 Отображение глав">📖 Chapters Display</h2>
                <p data-en="Available chapters in the platform:" data-he="הפרקים הזמינים בפלטפורמה:" data-ru="Доступные главы на платформе:">Available chapters in the platform:</p>
                <div id="chapters-display" class="loading" data-en="Loading chapters..." data-he="טוען פרקים..." data-ru="Загрузка глав...">Loading chapters...</div>
            </div>
        </div>
    </div>

    <script>
        // Language switching functionality
        let currentLanguage = 'en';

        function switchLanguage(lang) {
            currentLanguage = lang;

            // Update HTML direction
            const htmlRoot = document.getElementById('html-root');
            const body = document.body;

            if (lang === 'he') {
                htmlRoot.setAttribute('dir', 'rtl');
                htmlRoot.setAttribute('lang', 'he');
                body.classList.add('rtl');
            } else {
                htmlRoot.setAttribute('dir', 'ltr');
                htmlRoot.setAttribute('lang', lang);
                body.classList.remove('rtl');
            }

            // Update active language button
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-lang') === lang) {
                    btn.classList.add('active');
                }
            });

            // Update all text elements
            document.querySelectorAll('[data-' + lang + ']').forEach(element => {
                const text = element.getAttribute('data-' + lang);
                if (text) {
                    element.textContent = text;
                }
            });

            // Update placeholders
            document.querySelectorAll('[data-placeholder-' + lang + ']').forEach(element => {
                const placeholder = element.getAttribute('data-placeholder-' + lang);
                if (placeholder) {
                    element.placeholder = placeholder;
                }
            });

            // Store language preference
            localStorage.setItem('preferred-language', lang);
        }

        // Load saved language preference
        function loadLanguagePreference() {
            const savedLang = localStorage.getItem('preferred-language') || 'en';
            switchLanguage(savedLang);
        }

        async function testAPI(endpoint, responseId) {
            const responseElement = document.getElementById(responseId);
            responseElement.style.display = 'block';
            responseElement.textContent = 'טוען...';
            
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = 'שגיאה: ' + error.message;
            }
        }
        
        async function loadChapters() {
            const container = document.getElementById('chapters-display');
            
            try {
                const response = await fetch('/public/chapters');
                const data = await response.json();
                
                if (data.chapters && data.chapters.length > 0) {
                    container.innerHTML = data.chapters.map(chapter => \`
                        <div class="chapter-card">
                            <div class="chapter-title">\${chapter.title}</div>
                            <div class="chapter-meta">
                                רמת קושי: \${chapter.difficulty_level} | 
                                זמן קריאה: \${chapter.reading_time_minutes} דקות |
                                תגיות: \${chapter.tags.join(', ')}
                            </div>
                            <div class="chapter-preview">\${chapter.preview_content}</div>
                            <span class="price-tag \${chapter.is_free ? 'free-tag' : ''}">
                                \${chapter.is_free ? 'חינם' : '$' + chapter.price}
                            </span>
                        </div>
                    \`).join('');
                } else {
                    container.innerHTML = '<p>לא נמצאו פרקים</p>';
                }
            } catch (error) {
                container.innerHTML = '<p>שגיאה בטעינת הפרקים: ' + error.message + '</p>';
            }
        }
        
        async function performSearch() {
            const searchInput = document.getElementById('search-input');
            const resultsContainer = document.getElementById('search-results');
            const query = searchInput.value.trim();

            if (!query) {
                alert('אנא הקלידו מילת חיפוש');
                return;
            }

            resultsContainer.style.display = 'block';
            resultsContainer.innerHTML = '<div class="loading">מחפש...</div>';

            try {
                const response = await fetch(\`/public/search?q=\${encodeURIComponent(query)}\`);
                const data = await response.json();

                if (data.results && data.results.length > 0) {
                    resultsContainer.innerHTML = \`
                        <h4>נמצאו \${data.total_found} תוצאות עבור "\${data.query}" (זמן חיפוש: \${data.search_time_ms}ms)</h4>
                        \${data.results.map(result => \`
                            <div class="chapter-card">
                                <div class="chapter-title">\${result.title}</div>
                                <div class="chapter-meta">
                                    סוג: \${result.type} |
                                    רמת קושי: \${result.difficulty_level || 'לא צוין'} |
                                    רלוונטיות: \${Math.round(result.relevance_score * 100)}%
                                </div>
                                <div class="chapter-preview">\${result.search_snippet}</div>
                                \${result.price ? \`<span class="price-tag \${result.is_free ? 'free-tag' : ''}">
                                    \${result.is_free ? 'חינם' : '$' + result.price}
                                </span>\` : ''}
                            </div>
                        \`).join('')}
                    \`;
                } else {
                    resultsContainer.innerHTML = '<p>לא נמצאו תוצאות עבור החיפוש שלכם</p>';
                }
            } catch (error) {
                resultsContainer.innerHTML = '<p>שגיאה בחיפוש: ' + error.message + '</p>';
            }
        }

        async function loadStats() {
            const container = document.getElementById('platform-stats');

            try {
                const response = await fetch('/public/stats');
                const data = await response.json();
                const stats = data.stats;

                container.innerHTML = \`
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div class="chapter-card">
                            <h4>📚 סה"כ פרקים</h4>
                            <div style="font-size: 2em; font-weight: bold; color: #4299e1;">\${stats.platform.total_chapters}</div>
                        </div>
                        <div class="chapter-card">
                            <h4>👥 משתמשים רשומים</h4>
                            <div style="font-size: 2em; font-weight: bold; color: #48bb78;">\${stats.platform.total_users}</div>
                        </div>
                        <div class="chapter-card">
                            <h4>⏰ שעות למידה</h4>
                            <div style="font-size: 2em; font-weight: bold; color: #ed8936;">\${stats.platform.total_reading_hours}</div>
                        </div>
                    </div>

                    <h4>פרקים פופולריים:</h4>
                    \${stats.popular_chapters.map(chapter => \`
                        <div class="chapter-card">
                            <div class="chapter-title">\${chapter.title}</div>
                            <div class="chapter-meta">
                                צפיות: \${chapter.views} |
                                השלמה: \${chapter.completion_rate}% |
                                דירוג: \${chapter.average_rating}/5
                            </div>
                        </div>
                    \`).join('')}

                    <h4>ביקורות:</h4>
                    \${stats.testimonials.map(testimonial => \`
                        <div class="chapter-card">
                            <div class="chapter-title">\${testimonial.name}</div>
                            <div class="chapter-preview">"\${testimonial.text}"</div>
                            <div class="chapter-meta">
                                דירוג: \${'⭐'.repeat(testimonial.rating)} |
                                קורס שהושלם: \${testimonial.course_completed}
                            </div>
                        </div>
                    \`).join('')}
                \`;
            } catch (error) {
                container.innerHTML = '<p>שגיאה בטעינת הסטטיסטיקות: ' + error.message + '</p>';
            }
        }

        async function loadSubscriptionPlans() {
            const container = document.getElementById('subscription-plans');

            try {
                const response = await fetch('/public/subscriptions/plans');
                const data = await response.json();

                container.innerHTML = \`
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        \${data.plans.map(plan => \`
                            <div class="chapter-card" style="border: \${plan.popular ? '3px solid #4299e1' : '1px solid #e0e0e0'};">
                                \${plan.popular ? '<div style="background: #4299e1; color: white; text-align: center; padding: 5px; margin: -20px -20px 15px -20px;">הכי פופולרי!</div>' : ''}
                                <div class="chapter-title">\${plan.name}</div>
                                <div style="font-size: 2em; font-weight: bold; color: #4299e1; margin: 10px 0;">
                                    $\${plan.price}
                                    <span style="font-size: 0.5em; color: #666;">/ \${plan.duration_months} חודשים</span>
                                </div>
                                <div class="chapter-preview">\${plan.description}</div>
                                <ul style="text-align: right; margin: 15px 0;">
                                    \${plan.features.map(feature => \`<li>\${feature}</li>\`).join('')}
                                </ul>
                                \${plan.savings_percentage > 0 ? \`<div class="price-tag">חיסכון של \${plan.savings_percentage}%!</div>\` : ''}
                            </div>
                        \`).join('')}
                    </div>
                    <p style="text-align: center; color: #666; margin-top: 20px;">\${data.currency_note}</p>
                \`;
            } catch (error) {
                container.innerHTML = '<p>שגיאה בטעינת תוכניות המנוי: ' + error.message + '</p>';
            }
        }

        // Add Enter key support for search
        document.addEventListener('DOMContentLoaded', function() {
            // Load language preference first
            loadLanguagePreference();

            // Then load content
            loadChapters();
            loadStats();
            loadSubscriptionPlans();

            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performSearch();
                    }
                });
            }
        });
    </script>
</body>
</html>
  `;

  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  res.send(html);
}
