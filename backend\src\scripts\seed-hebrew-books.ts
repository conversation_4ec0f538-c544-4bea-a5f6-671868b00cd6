import { ExecArgs } from "@medusajs/framework/types"
import { BO<PERSON>_MODULE } from "../modules/book"

const seedHebrewBooks = async ({ container }: ExecArgs) => {
  console.log("Seeding Hebrew Book Store data...")
  
  const bookService = container.resolve(BOOK_MODULE)
  
  // Create sample chapters
  const chapters = [
    {
      title: "פרק ראשון - מבוא לעברית",
      content: `
        <h1>מבוא לעברית</h1>
        <p>ברוכים הבאים לפרק הראשון בלימוד עברית!</p>
        <p>בפרק זה נלמד על:</p>
        <ul>
          <li>האלפבית העברי</li>
          <li>צלילים בסיסיים</li>
          <li>מילים ראשונות</li>
        </ul>
        <h2>האלפבית העברי</h2>
        <p>האלפבית העברי מכיל 22 אותיות:</p>
        <p>א ב ג ד ה ו ז ח ט י כ ל מ נ ס ע פ צ ק ר ש ת</p>
        <h2>תרגילים</h2>
        <p>נסו לקרוא את המילים הבאות:</p>
        <ul>
          <li>שלום</li>
          <li>תודה</li>
          <li>בוקר טוב</li>
        </ul>
      `,
      preview_content: "ברוכים הבאים לפרק הראשון בלימוד עברית! בפרק זה נלמד על האלפבית העברי, צלילים בסיסיים ומילים ראשונות...",
      difficulty_level: "beginner",
      order_in_book: 1,
      is_published: true,
      language: "he",
      price: 9.99,
      is_free: true, // First chapter is free
      reading_time_minutes: 15,
      tags: JSON.stringify(["אלפבית", "בסיסי", "מתחילים"])
    },
    {
      title: "פרק שני - מילים בסיסיות",
      content: `
        <h1>מילים בסיסיות בעברית</h1>
        <p>בפרק זה נרחיב את אוצר המילים שלנו.</p>
        <h2>משפחה</h2>
        <ul>
          <li>אבא - Father</li>
          <li>אמא - Mother</li>
          <li>אח - Brother</li>
          <li>אחות - Sister</li>
        </ul>
        <h2>צבעים</h2>
        <ul>
          <li>אדום - Red</li>
          <li>כחול - Blue</li>
          <li>ירוק - Green</li>
          <li>צהוב - Yellow</li>
        </ul>
        <h2>מספרים</h2>
        <ul>
          <li>אחד - One</li>
          <li>שניים - Two</li>
          <li>שלושה - Three</li>
          <li>ארבעה - Four</li>
        </ul>
      `,
      preview_content: "בפרק זה נרחיב את אוצר המילים שלנו ונלמד מילים בסיסיות על משפחה, צבעים ומספרים...",
      difficulty_level: "beginner",
      order_in_book: 2,
      is_published: true,
      language: "he",
      price: 12.99,
      is_free: false,
      reading_time_minutes: 20,
      tags: JSON.stringify(["מילים", "משפחה", "צבעים", "מספרים"])
    },
    {
      title: "פרק שלישי - משפטים ראשונים",
      content: `
        <h1>בניית משפטים ראשונים</h1>
        <p>עכשיו שאנחנו יודעים מילים בסיסיות, בואו נלמד לבנות משפטים.</p>
        <h2>מבנה המשפט</h2>
        <p>במשפט עברי בסיסי יש לנו:</p>
        <ul>
          <li>נושא (מי עושה את הפעולה)</li>
          <li>פועל (מה נעשה)</li>
          <li>מושא (על מי או מה)</li>
        </ul>
        <h2>דוגמאות</h2>
        <ul>
          <li>אני אוהב ספרים</li>
          <li>היא קוראת עיתון</li>
          <li>הם הולכים הביתה</li>
        </ul>
        <h2>תרגילים</h2>
        <p>נסו לבנות משפטים עם המילים שלמדנו:</p>
        <ul>
          <li>אבא + קורא + ספר</li>
          <li>אמא + אוהבת + פרחים</li>
        </ul>
      `,
      preview_content: "עכשיו שאנחנו יודעים מילים בסיסיות, בואו נלמד לבנות משפטים ראשונים...",
      difficulty_level: "intermediate",
      order_in_book: 3,
      is_published: true,
      language: "he",
      price: 15.99,
      is_free: false,
      reading_time_minutes: 25,
      tags: JSON.stringify(["משפטים", "דקדוק", "תרגילים"])
    }
  ]

  console.log("Creating chapters...")
  for (const chapterData of chapters) {
    try {
      const chapter = await bookService.createChapter(chapterData)
      console.log(`Created chapter: ${chapter.title}`)
    } catch (error) {
      console.error(`Error creating chapter ${chapterData.title}:`, error)
    }
  }

  console.log("Hebrew Book Store seeding completed!")
}

export default seedHebrewBooks
