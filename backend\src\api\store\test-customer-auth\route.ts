import {
  MedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"

export const POST = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { email, password } = req.body

    console.log('🧪 Testing customer auth flow for:', email)

    const publishableKey = process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_a00302b727fcf4f5285975080843c4bb959bc257a8a7e070ea0967c396c64505'
    const backendUrl = process.env.MEDUSA_BACKEND_URL || 'http://localhost:9000'

    console.log('🔑 Using publishable key:', publishableKey.substring(0, 20) + '...')
    console.log('🌐 Using backend URL:', backendUrl)

    // Step 1: Try to get registration token
    console.log('🔄 Step 1: Getting registration token...')
    const registerResponse = await fetch(`${backendUrl}/auth/customer/emailpass/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': publishableKey,
      },
      body: JSON.stringify({ email, password }),
    })

    const registerData = await registerResponse.json()
    console.log('📝 Registration response:', registerResponse.status, registerData)

    if (!registerResponse.ok) {
      return res.status(400).json({
        success: false,
        step: 'registration',
        error: registerData,
        message: 'Registration failed',
      })
    }

    const registrationToken = registerData.token

    // Step 2: Create customer with registration token
    console.log('🔄 Step 2: Creating customer with token...')
    const customerResponse = await fetch(`${backendUrl}/store/customers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${registrationToken}`,
        'x-publishable-api-key': publishableKey,
      },
      body: JSON.stringify({
        email,
        first_name: 'Test',
        last_name: 'Customer',
      }),
    })

    const customerData = await customerResponse.json()
    console.log('👤 Customer creation response:', customerResponse.status, customerData)

    if (!customerResponse.ok) {
      return res.status(400).json({
        success: false,
        step: 'customer_creation',
        error: customerData,
        message: 'Customer creation failed',
      })
    }

    // Step 3: Login to get authentication token
    console.log('🔄 Step 3: Logging in to get auth token...')
    const loginResponse = await fetch(`${backendUrl}/auth/customer/emailpass`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': publishableKey,
      },
      body: JSON.stringify({ email, password }),
    })

    const loginData = await loginResponse.json()
    console.log('🔑 Login response:', loginResponse.status, loginData)

    if (!loginResponse.ok) {
      return res.status(400).json({
        success: false,
        step: 'login',
        error: loginData,
        message: 'Login failed',
      })
    }

    const authToken = loginData.token

    // Step 4: Test authenticated request
    console.log('🔄 Step 4: Testing authenticated request...')
    const meResponse = await fetch(`${backendUrl}/store/customers/me`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'x-publishable-api-key': publishableKey,
      },
    })

    const meData = await meResponse.json()
    console.log('👤 Me response:', meResponse.status, meData)

    res.json({
      success: true,
      steps: {
        registration: { status: registerResponse.status, data: registerData },
        customer_creation: { status: customerResponse.status, data: customerData },
        login: { status: loginResponse.status, data: loginData },
        authenticated_request: { status: meResponse.status, data: meData },
      },
      final_auth_token: authToken,
      message: 'Customer auth flow test completed',
    })
  } catch (error) {
    console.error('❌ Customer auth test failed:', error)
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Customer auth test failed',
    })
  }
}
