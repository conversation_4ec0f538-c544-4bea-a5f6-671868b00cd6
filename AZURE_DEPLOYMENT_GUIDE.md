# 🚀 Azure Deployment Guide - Hebrew Book Store

This guide will help you deploy your MedusaJS Hebrew Book Store project to Microsoft Azure.

## 📋 Prerequisites

- Azure account with active subscription
- Azure CLI installed locally
- Docker installed locally
- Git repository with your project

## 🏗️ Architecture Overview

Your deployment will include:
- **Azure Container Apps**: For MedusaJS backend
- **Azure Database for PostgreSQL**: Database
- **Azure Static Web Apps**: For frontend (optional)
- **Azure Storage Account**: For file uploads
- **Azure Container Registry**: For Docker images

## 🔧 Step 1: Prepare Your Project

### 1.1 Create Dockerfile for Backend

Create `backend/Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN yarn build

# Expose port
EXPOSE 9000

# Start the application
CMD ["yarn", "start"]
```

### 1.2 Create .dockerignore

Create `backend/.dockerignore`:

```
node_modules
.git
.env
.env.local
dist
build
coverage
.nyc_output
```

### 1.3 Update Environment Variables

Create `backend/.env.production.template`:

```bash
# Database
DATABASE_URL=********************************************/database_name

# Redis (optional)
REDIS_URL=redis://hostname:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key

# Cookie Secret
COOKIE_SECRET=your-super-secret-cookie-key

# Admin CORS
ADMIN_CORS=https://your-domain.azurecontainerapps.io

# Store CORS
STORE_CORS=https://your-frontend-domain.com

# File Storage
AZURE_STORAGE_ACCOUNT_NAME=your-storage-account
AZURE_STORAGE_ACCOUNT_KEY=your-storage-key
AZURE_STORAGE_CONTAINER_NAME=medusa-files
```

## 🗄️ Step 2: Set Up Azure Database

### 2.1 Create PostgreSQL Database

```bash
# Login to Azure
az login

# Create resource group
az group create --name rg-hebrew-bookstore --location "East US"

# Create PostgreSQL server
az postgres flexible-server create \
  --resource-group rg-hebrew-bookstore \
  --name hebrew-bookstore-db \
  --location "East US" \
  --admin-user medusaadmin \
  --admin-password "YourSecurePassword123!" \
  --sku-name Standard_B1ms \
  --tier Burstable \
  --storage-size 32 \
  --version 14

# Create database
az postgres flexible-server db create \
  --resource-group rg-hebrew-bookstore \
  --server-name hebrew-bookstore-db \
  --database-name medusa_store

# Configure firewall (allow Azure services)
az postgres flexible-server firewall-rule create \
  --resource-group rg-hebrew-bookstore \
  --name hebrew-bookstore-db \
  --rule-name AllowAzureServices \
  --start-ip-address 0.0.0.0 \
  --end-ip-address 0.0.0.0
```

### 2.2 Get Database Connection String

```bash
az postgres flexible-server show-connection-string \
  --server-name hebrew-bookstore-db \
  --database-name medusa_store \
  --admin-user medusaadmin \
  --admin-password "YourSecurePassword123!"
```

## 📦 Step 3: Set Up Azure Container Registry

```bash
# Create container registry
az acr create \
  --resource-group rg-hebrew-bookstore \
  --name hebrewbookstoreacr \
  --sku Basic \
  --admin-enabled true

# Get login credentials
az acr credential show --name hebrewbookstoreacr
```

## 🐳 Step 4: Build and Push Docker Image

```bash
# Navigate to backend directory
cd backend

# Login to ACR
az acr login --name hebrewbookstoreacr

# Build and push image
docker build -t hebrewbookstoreacr.azurecr.io/medusa-backend:latest .
docker push hebrewbookstoreacr.azurecr.io/medusa-backend:latest
```

## ☁️ Step 5: Create Azure Storage Account

```bash
# Create storage account
az storage account create \
  --name hebrewbookstorage \
  --resource-group rg-hebrew-bookstore \
  --location "East US" \
  --sku Standard_LRS \
  --kind StorageV2

# Create container for file uploads
az storage container create \
  --name medusa-files \
  --account-name hebrewbookstorage \
  --public-access blob

# Get storage account key
az storage account keys list \
  --resource-group rg-hebrew-bookstore \
  --account-name hebrewbookstorage
```

## 🚀 Step 6: Deploy to Azure Container Apps

### 6.1 Create Container Apps Environment

```bash
# Install Container Apps extension
az extension add --name containerapp

# Create Container Apps environment
az containerapp env create \
  --name hebrew-bookstore-env \
  --resource-group rg-hebrew-bookstore \
  --location "East US"
```

### 6.2 Deploy MedusaJS Backend

```bash
# Create container app
az containerapp create \
  --name medusa-backend \
  --resource-group rg-hebrew-bookstore \
  --environment hebrew-bookstore-env \
  --image hebrewbookstoreacr.azurecr.io/medusa-backend:latest \
  --registry-server hebrewbookstoreacr.azurecr.io \
  --registry-username hebrewbookstoreacr \
  --registry-password "YOUR_ACR_PASSWORD" \
  --target-port 9000 \
  --ingress external \
  --min-replicas 1 \
  --max-replicas 3 \
  --cpu 1.0 \
  --memory 2.0Gi \
  --env-vars \
    DATABASE_URL="****************************************************************************************************/medusa_store" \
    JWT_SECRET="your-super-secret-jwt-key" \
    COOKIE_SECRET="your-super-secret-cookie-key" \
    AZURE_STORAGE_ACCOUNT_NAME="hebrewbookstorage" \
    AZURE_STORAGE_ACCOUNT_KEY="YOUR_STORAGE_KEY" \
    AZURE_STORAGE_CONTAINER_NAME="medusa-files" \
    NODE_ENV="production"
```

## 🌐 Step 7: Deploy Frontend (Optional)

### 7.1 Prepare Frontend for Deployment

Update `frontend/.env.production`:

```bash
VITE_MEDUSA_BACKEND_URL=https://medusa-backend.YOUR_CONTAINER_APP_URL.azurecontainerapps.io
```

### 7.2 Deploy to Azure Static Web Apps

```bash
# Create static web app
az staticwebapp create \
  --name hebrew-bookstore-frontend \
  --resource-group rg-hebrew-bookstore \
  --source https://github.com/YOUR_USERNAME/YOUR_REPO \
  --location "East US2" \
  --branch main \
  --app-location "/frontend" \
  --output-location "dist"
```

## 🔒 Step 8: Configure SSL and Custom Domain (Optional)

### 8.1 Add Custom Domain to Container App

```bash
# Add custom domain
az containerapp hostname add \
  --hostname api.yourdomain.com \
  --name medusa-backend \
  --resource-group rg-hebrew-bookstore
```

### 8.2 Configure SSL Certificate

```bash
# Bind SSL certificate
az containerapp hostname bind \
  --hostname api.yourdomain.com \
  --name medusa-backend \
  --resource-group rg-hebrew-bookstore \
  --certificate-type Managed
```

## 📊 Step 9: Set Up Monitoring (Optional)

```bash
# Create Application Insights
az monitor app-insights component create \
  --app hebrew-bookstore-insights \
  --location "East US" \
  --resource-group rg-hebrew-bookstore \
  --application-type web

# Get instrumentation key
az monitor app-insights component show \
  --app hebrew-bookstore-insights \
  --resource-group rg-hebrew-bookstore \
  --query instrumentationKey
```

## 🔄 Step 10: Set Up CI/CD with GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Azure

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Login to Azure
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: Build and push Docker image
      run: |
        cd backend
        az acr login --name hebrewbookstoreacr
        docker build -t hebrewbookstoreacr.azurecr.io/medusa-backend:${{ github.sha }} .
        docker push hebrewbookstoreacr.azurecr.io/medusa-backend:${{ github.sha }}
    
    - name: Deploy to Container Apps
      run: |
        az containerapp update \
          --name medusa-backend \
          --resource-group rg-hebrew-bookstore \
          --image hebrewbookstoreacr.azurecr.io/medusa-backend:${{ github.sha }}
```

## 🧪 Step 11: Testing Your Deployment

1. **Test Backend API**:
   ```bash
   curl https://medusa-backend.YOUR_URL.azurecontainerapps.io/health
   ```

2. **Test Admin Panel**:
   Visit: `https://medusa-backend.YOUR_URL.azurecontainerapps.io/app`

3. **Test Store API**:
   ```bash
   curl https://medusa-backend.YOUR_URL.azurecontainerapps.io/store/products
   ```

## 💰 Cost Optimization Tips

1. **Use Burstable Database Tier**: Start with Standard_B1ms
2. **Configure Auto-scaling**: Set appropriate min/max replicas
3. **Use Azure Storage Cool Tier**: For infrequently accessed files
4. **Monitor Usage**: Set up billing alerts

## 🔧 Troubleshooting

### Common Issues:

1. **Database Connection Issues**:
   - Check firewall rules
   - Verify connection string
   - Ensure SSL is configured

2. **Container App Won't Start**:
   - Check environment variables
   - Review container logs: `az containerapp logs show`

3. **File Upload Issues**:
   - Verify storage account permissions
   - Check CORS settings

### Useful Commands:

```bash
# View container app logs
az containerapp logs show --name medusa-backend --resource-group rg-hebrew-bookstore

# Scale container app
az containerapp update --name medusa-backend --resource-group rg-hebrew-bookstore --min-replicas 2

# Update environment variables
az containerapp update --name medusa-backend --resource-group rg-hebrew-bookstore --set-env-vars NEW_VAR=value
```

## 📚 Next Steps

1. Set up automated backups for PostgreSQL
2. Configure Azure CDN for better performance
3. Set up monitoring and alerting
4. Implement proper logging strategy
5. Configure Azure Key Vault for secrets management

Your Hebrew Book Store is now deployed on Azure! 🎉
