import {
  AbstractFulfillmentProviderService,
  MedusaError,
} from "@medusajs/framework/utils"

type DigitalFulfillmentOptions = {
  name: string
}

type CreateFulfillmentData = {
  items: Array<{
    id: string
    quantity: number
  }>
  order: any
  shipping_option: any
}

type CancelFulfillmentData = {
  fulfillment: any
}

class DigitalFulfillmentProviderService extends AbstractFulfillmentProviderService {
  static identifier = "digital-fulfillment"
  
  protected options_: DigitalFulfillmentOptions

  constructor(container: any, options: DigitalFulfillmentOptions) {
    super()
    this.options_ = options
  }

  async getFulfillmentOptions(): Promise<any[]> {
    return [
      {
        id: "digital-delivery",
        name: "Digital Delivery",
        type: "digital",
        price: 0, // Digital products are delivered for free
      },
    ]
  }

  async validateFulfillmentData(
    optionData: any,
    data: any,
    cart: any
  ): Promise<any> {
    // For digital products, no validation needed
    return {
      ...data,
      validated: true,
    }
  }

  async validateOption(data: any): Promise<boolean> {
    // Digital delivery is always valid
    return true
  }

  async canCalculate(data: any): Promise<boolean> {
    // We can always calculate for digital products
    return true
  }

  async calculatePrice(
    optionData: Record<string, unknown>,
    data: Record<string, unknown>,
    context: any // Ideally, use the correct type: CartPropsForFulfillment & ({ [k: string]: unknown; from_location?: StockLocationDTO | undefined; } & CalculatedRMAShippingContext)
  ): Promise<{ amount: number; details?: Record<string, unknown> }> {
    // Digital delivery is free
    return {
      amount: 0,
      details: {
        reason: "Digital delivery is always free"
      }
    }
  }

  async createFulfillment(
    data: CreateFulfillmentData,
    items: any[],
    order: any,
    fulfillment: any
  ): Promise<any> {
    console.log('🚀 Creating digital fulfillment for order:', order.id)
    
    // For digital products, fulfillment is instant
    // The actual delivery (sending download links) will be handled by workflows
    return {
      external_id: `digital_${order.id}_${Date.now()}`,
      data: {
        type: "digital",
        items: items.map(item => ({
          id: item.id,
          quantity: item.quantity,
        })),
        order_id: order.id,
        created_at: new Date().toISOString(),
      },
    }
  }

  async cancelFulfillment(data: CancelFulfillmentData): Promise<any> {
    console.log('❌ Canceling digital fulfillment:', data.fulfillment.id)
    
    // For digital products, cancellation means revoking access
    // This could involve disabling download links, etc.
    return {
      external_id: data.fulfillment.external_id,
      data: {
        ...data.fulfillment.data,
        cancelled_at: new Date().toISOString(),
      },
    }
  }

  async createReturn(returnData: any): Promise<any> {
    throw new MedusaError(
      MedusaError.Types.NOT_ALLOWED,
      "Digital products cannot be returned"
    )
  }

  async getReturnDocuments(data: any): Promise<any> {
    throw new MedusaError(
      MedusaError.Types.NOT_ALLOWED,
      "Digital products do not have return documents"
    )
  }

  async getShipmentDocuments(data: any): Promise<any> {
    // Digital products don't have physical shipment documents
    return {
      tracking_number: data.external_id,
      tracking_url: null,
      label_url: null,
    }
  }

  async retrieveDocuments(
    fulfillmentData: any,
    documentType: string
  ): Promise<any> {
    // Digital products don't have physical documents
    return null
  }
}

export default DigitalFulfillmentProviderService
