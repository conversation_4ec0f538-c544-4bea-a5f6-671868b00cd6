import { ExecArgs } from "@medusajs/framework/types"
import {
  ContainerRegistrationKeys,
  ModuleRegistrationName,
} from "@medusajs/framework/utils"

const setupDigitalShipping = async ({ container }: ExecArgs) => {
  console.log("🚢 Setting Up Digital Product Shipping Options...")
  console.log("=" .repeat(60))
  
  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    
    // 1. Find digital shipping profile
    console.log("1️⃣ Finding Digital Shipping Profile...")
    const { data: shippingProfiles } = await query.graph({
      entity: "shipping_profile",
      fields: ["*"],
      filters: {
        type: "Digital",
      },
    })

    if (shippingProfiles.length === 0) {
      console.log("❌ No digital shipping profile found. Creating one...")
      
      // Create digital shipping profile
      const shippingModuleService = container.resolve(
        ModuleRegistrationName.FULFILLMENT
      )
      
      const digitalProfile = await shippingModuleService.createShippingProfiles({
        name: "Digital Products",
        type: "Digital",
      })
      
      console.log(`✅ Created digital shipping profile: ${digitalProfile.id}`)
    } else {
      console.log(`✅ Found digital shipping profile: ${shippingProfiles[0].name} (${shippingProfiles[0].id})`)
    }

    const digitalProfile = shippingProfiles[0] || digitalProfile

    // 2. Check for existing digital shipping options
    console.log("\n2️⃣ Checking Existing Digital Shipping Options...")
    const { data: existingOptions } = await query.graph({
      entity: "shipping_option",
      fields: ["*", "shipping_profile.*"],
      filters: {
        provider_id: "digital-fulfillment",
      },
    })

    if (existingOptions.length > 0) {
      console.log(`✅ Found ${existingOptions.length} existing digital shipping options`)
      for (const option of existingOptions) {
        console.log(`   📦 ${option.name} (${option.id})`)
      }
      console.log("✅ Digital shipping is already configured!")
      return
    }

    // 3. Create digital shipping option
    console.log("\n3️⃣ Creating Digital Shipping Option...")
    
    const fulfillmentModuleService = container.resolve(
      ModuleRegistrationName.FULFILLMENT
    )

    // Create shipping option type for digital delivery
    const shippingOptionType = await fulfillmentModuleService.createShippingOptionTypes({
      label: "Digital Delivery",
      description: "Instant digital product delivery",
      code: "digital-delivery",
    })

    console.log(`✅ Created shipping option type: ${shippingOptionType.id}`)

    // Create the shipping option
    const digitalShippingOption = await fulfillmentModuleService.createShippingOptions({
      name: "Digital Product Delivery",
      price_type: "flat",
      service_zone_id: null, // Digital products don't need zones
      shipping_profile_id: digitalProfile.id,
      provider_id: "digital-fulfillment",
      type_id: shippingOptionType.id,
      data: {
        delivery_method: "digital",
        instant_delivery: true,
      },
      prices: [
        {
          currency_code: "USD",
          amount: 0, // Free digital delivery
        },
        {
          currency_code: "ILS",
          amount: 0, // Free digital delivery
        },
      ],
    })

    console.log(`✅ Created digital shipping option: ${digitalShippingOption.id}`)

    // 4. Verify setup
    console.log("\n4️⃣ Verifying Setup...")
    const { data: newOptions } = await query.graph({
      entity: "shipping_option",
      fields: ["*", "shipping_profile.*", "type.*"],
      filters: {
        provider_id: "digital-fulfillment",
      },
    })

    console.log(`✅ Verification: Found ${newOptions.length} digital shipping options`)
    for (const option of newOptions) {
      console.log(`   📦 ${option.name}`)
      console.log(`      - Provider: ${option.provider_id}`)
      console.log(`      - Profile: ${option.shipping_profile?.name}`)
      console.log(`      - Type: ${option.type?.label}`)
    }

    console.log("\n" + "=" .repeat(60))
    console.log("✅ DIGITAL SHIPPING SETUP COMPLETED!")
    console.log("=" .repeat(60))
    console.log("📋 What was created:")
    console.log("   - Digital shipping option type")
    console.log("   - Digital product delivery shipping option")
    console.log("   - Free pricing for USD and ILS")
    console.log("")
    console.log("🔄 Next steps:")
    console.log("   1. Place a new order with digital products")
    console.log("   2. The order should automatically get digital shipping")
    console.log("   3. Fulfillment should be created automatically")
    console.log("   4. Auto-fulfillment workflow should trigger")
    console.log("   5. Order status should update to 'fulfilled'")

  } catch (error) {
    console.error("❌ CRITICAL ERROR:", error.message)
    console.error("Stack:", error.stack)
  }
}

export default setupDigitalShipping
