import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { q, type = "all", limit = 10 } = req.query

    if (!q || typeof q !== "string" || q.trim().length === 0) {
      return res.status(400).json({
        message: "Search query is required",
        error: "Missing or empty search query parameter 'q'"
      })
    }

    const searchQuery = q.trim().toLowerCase()

    // Mock search data
    const allContent = [
      // Chapters
      {
        type: "chapter",
        id: "chapter_1",
        title: "פרק ראשון - מבוא לעברית",
        content: "ברוכים הבאים לפרק הראשון בלימוד עברית האלפבית העברי צלילים בסיסיים מילים ראשונות",
        difficulty_level: "beginner",
        tags: ["אלפבית", "בסיסי", "מתחילים"],
        is_free: true,
        price: 9.99
      },
      {
        type: "chapter", 
        id: "chapter_2",
        title: "פרק שני - מילים בסיסיות",
        content: "מילים בסיסיות בעברית משפחה אבא אמא אח אחות צבעים אדום כחול ירוק צהוב מספרים אחד שניים שלושה",
        difficulty_level: "beginner",
        tags: ["מילים", "משפחה", "צבעים", "מספרים"],
        is_free: false,
        price: 12.99
      },
      {
        type: "chapter",
        id: "chapter_3", 
        title: "פרק שלישי - משפטים ראשונים",
        content: "בניית משפטים ראשונים מבנה המשפט נושא פועל מושא דוגמאות אני אוהב ספרים היא קוראת עיתון",
        difficulty_level: "intermediate",
        tags: ["משפטים", "דקדוק", "תרגילים"],
        is_free: false,
        price: 15.99
      },
      // Vocabulary
      {
        type: "vocabulary",
        id: "vocab_family",
        title: "אוצר מילים: משפחה",
        content: "אבא אמא אח אחות סבא סבתא דוד דודה בן בת",
        category: "משפחה",
        difficulty_level: "beginner"
      },
      {
        type: "vocabulary",
        id: "vocab_colors",
        title: "אוצר מילים: צבעים",
        content: "אדום כחול ירוק צהוב סגול כתום ורוד חום שחור לבן",
        category: "צבעים",
        difficulty_level: "beginner"
      },
      // Grammar rules
      {
        type: "grammar",
        id: "grammar_sentence_structure",
        title: "מבנה המשפט בעברית",
        content: "מבנה המשפט הבסיסי בעברית נושא פועל מושא סדר המילים דקדוק",
        difficulty_level: "intermediate"
      }
    ]

    // Search logic
    let results = allContent.filter(item => {
      const searchableText = `${item.title} ${item.content} ${item.tags?.join(' ') || ''}`.toLowerCase()
      return searchableText.includes(searchQuery)
    })

    // Filter by type if specified
    if (type !== "all") {
      results = results.filter(item => item.type === type)
    }

    // Limit results
    results = results.slice(0, parseInt(limit as string))

    // Add search relevance score (mock)
    results = results.map(item => ({
      ...item,
      relevance_score: Math.random() * 0.3 + 0.7, // Mock score between 0.7-1.0
      search_snippet: item.content.substring(0, 150) + "..."
    }))

    // Sort by relevance
    results.sort((a, b) => b.relevance_score - a.relevance_score)

    res.json({
      query: searchQuery,
      results,
      total_found: results.length,
      search_time_ms: Math.floor(Math.random() * 50) + 10, // Mock search time
      suggestions: searchQuery.length < 3 ? [
        "אלפבית",
        "משפחה", 
        "צבעים",
        "מספרים",
        "משפטים"
      ] : []
    })
  } catch (error) {
    console.error("Error in search:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}
