<script lang="ts">
	import { cartStore, cartActions, cartItemCount, cartTotal, cartLoading, cartSynced } from '$lib/stores/cart'
	import { createEventDispatcher } from 'svelte'
	import { goto } from '$app/navigation'
	import { _ } from '$lib/i18n'
	import type { Cart } from '$lib/types/products'
	import { formatPriceWithCurrency, currentCurrency } from '$lib/stores/currency'
	import { isAuthenticated, currentCustomer } from '$lib/stores/auth'

	export let isOpen = false

	const dispatch = createEventDispatcher()
	let cart: Cart

	$: cart = $cartStore
	$: itemCount = $cartItemCount
	$: total = $cartTotal
	$: currency = $currentCurrency

	function closeCart() {
		isOpen = false
		dispatch('close')
	}

	function updateQuantity(productId: string, quantity: number) {
		cartActions.updateQuantity(productId, quantity)
	}

	async function removeItem(productId: string) {
		await cartActions.removeItem(productId)
	}

	function clearCart() {
		cartActions.clearCart()
	}

	function proceedToCheckout() {
		// Check if user is authenticated
		if (!$isAuthenticated) {
			// TODO: Show login modal or redirect to login
			alert($_('checkout.login_required'))
			return
		}

		// Check if cart has items
		if (!cart.items || cart.items.length === 0) {
			alert($_('checkout.empty_cart'))
			return
		}

		// Close cart and navigate to checkout
		closeCart()
		goto('/checkout')
	}
</script>

<!-- Cart Overlay -->
{#if isOpen}
	<div class="fixed inset-0 z-50 overflow-hidden">
		<!-- Background overlay -->
		<div class="absolute inset-0 bg-black bg-opacity-50" on:click={closeCart}></div>
		
		<!-- Cart panel -->
		<div class="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
			<div class="flex h-full flex-col">
				<!-- Header -->
				<div class="flex items-center justify-between border-b border-gray-200 px-4 py-6">
					<div class="flex items-center space-x-2">
						<h2 class="text-lg font-medium text-gray-900">{$_('cart.title')}</h2>

						<!-- Cart status indicators -->
						{#if $cartLoading}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
						{:else if $isAuthenticated && $cartSynced}
							<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
								✓ Synced
							</span>
						{:else if $isAuthenticated && !$cartSynced}
							<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
								⚠ Local
							</span>
						{:else}
							<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
								📱 Guest
							</span>
						{/if}
					</div>

					<button
						type="button"
						class="text-gray-400 hover:text-gray-500"
						on:click={closeCart}
					>
						<span class="sr-only">Close cart</span>
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				</div>

				<!-- Cart items -->
				<div class="flex-1 overflow-y-auto px-4 py-6">
					{#if cart.items.length === 0}
						<div class="text-center">
							<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
							</svg>
							<h3 class="mt-2 text-sm font-medium text-gray-900">{$_('cart.empty_title')}</h3>
							<p class="mt-1 text-sm text-gray-500">{$_('cart.empty_description')}</p>
							<div class="mt-6">
								<button
									type="button"
									class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
									on:click={closeCart}
								>
									{$_('cart.continue_shopping')}
								</button>
							</div>
						</div>
					{:else}
						<div class="space-y-6">
							{#each cart.items as item (item.product_id)}
								<div class="flex items-center space-x-4">
									<!-- Product image -->
									<div class="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
										{#if item.product.cover_image}
											<img src={item.product.cover_image} alt={item.product.title} class="h-full w-full object-cover object-center" />
										{:else}
											<div class="flex h-full w-full items-center justify-center bg-gray-100">
												<svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
												</svg>
											</div>
										{/if}
									</div>

									<!-- Product details -->
									<div class="flex-1 min-w-0">
										<h4 class="text-sm font-medium text-gray-900 truncate">{item.product.title}</h4>
										<p class="text-sm text-gray-500 truncate">{item.product.short_description}</p>
										<p class="text-sm font-medium text-gray-900">{formatPriceWithCurrency(item.product.price )}</p>
									</div>

									<!-- Quantity controls -->
									<div class="flex items-center space-x-2">
										<button
											type="button"
											class="h-8 w-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
											on:click={() => updateQuantity(item.product_id, item.quantity - 1)}
										>
											<svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
											</svg>
										</button>
										<span class="text-sm font-medium text-gray-900 w-8 text-center">{item.quantity}</span>
										<button
											type="button"
											class="h-8 w-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
											on:click={() => updateQuantity(item.product_id, item.quantity + 1)}
										>
											<svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
											</svg>
										</button>
									</div>

									<!-- Remove button -->
									<button
										type="button"
										class="text-red-400 hover:text-red-500"
										on:click={() => removeItem(item.product_id)}
									>
										<svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
										</svg>
									</button>
								</div>
							{/each}
						</div>

						<!-- Clear cart button -->
						{#if cart.items.length > 0}
							<div class="mt-6 pt-6 border-t border-gray-200">
								<button
									type="button"
									class="text-sm text-red-600 hover:text-red-500"
									on:click={clearCart}
								>
									{$_('cart.clear_cart')}
								</button>
							</div>
						{/if}
					{/if}
				</div>

				<!-- Footer with total and checkout -->
				{#if cart.items.length > 0}
					<div class="border-t border-gray-200 px-4 py-6">
						<!-- Discount section -->
						{#if cart.discount_amount}
							<div class="flex justify-between text-sm text-gray-600 mb-2">
								<span>Subtotal:</span>
								<span>{formatPriceWithCurrency(total + cart.discount_amount)}</span>
							</div>
							<div class="flex justify-between text-sm text-green-600 mb-2">
								<span>Discount ({cart.discount_code}):</span>
								<span>-{formatPriceWithCurrency(cart.discount_amount)}</span>
							</div>
						{/if}

						<!-- Total -->
						<div class="flex justify-between text-base font-medium text-gray-900 mb-6">
							<span>{$_('cart.total')}</span>
							<span>{formatPriceWithCurrency(total)}</span>
						</div>

						<!-- Checkout button -->
						<button
							type="button"
							class="w-full rounded-md border border-transparent bg-blue-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
							on:click={proceedToCheckout}
						>
							{$_('cart.proceed_to_checkout')}
						</button>

						<!-- Continue shopping -->
						<div class="mt-6 text-center">
							<button
								type="button"
								class="text-sm font-medium text-blue-600 hover:text-blue-500"
								on:click={closeCart}
							>
								{$_('cart.continue_shopping')}
							</button>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}
