import {
  createWorkflow,
  transform,
  WorkflowResponse,
  when,
} from "@medusajs/framework/workflows-sdk"
import {
  useQueryGraphStep,
} from "@medusajs/medusa/core-flows"
import { createDigitalProductOrderStep } from "./steps/create-digital-product-order"
import { sendDigitalOrderNotificationStep } from "./steps/send-digital-order-notification"
import { markFulfillmentShippedStep } from "./steps/mark-fulfillment-shipped"

type WorkflowInput = {
  order_id: string
  fulfillment_id: string
}

const fulfillDigitalOrderWorkflow = createWorkflow(
  "fulfill-digital-order",
  (input: WorkflowInput) => {
    console.log('🚀 Starting digital order fulfillment workflow')
    console.log('  - Order ID:', input.order_id)
    console.log('  - Fulfillment ID:', input.fulfillment_id)
    // Get order details with digital products
    const { data: orders } = useQueryGraphStep({
      entity: "order",
      fields: [
        "*",
        "items.*",
        "items.variant.*",
        "items.variant.digital_product.*",
        "items.variant.digital_product.medias.*",
        "customer.*",
        "billing_address.*",
        "fulfillments.*",
      ],
      filters: {
        id: input.order_id,
      },
      options: {
        throwIfKeyNotFound: true,
      },
    })

    const order = transform({ orders }, (data) => data.orders[0])

    // Extract digital products from order items
    const digitalProducts = transform({
      order,
    }, (data) => {
      const products: any[] = []

      if (data.order.items) {
        data.order.items.forEach((item: any) => {
          if (item?.variant?.digital_product) {
            products.push({
              id: item.variant.digital_product.id,
              name: item.variant.digital_product.name,
              medias: item.variant.digital_product.medias || [],
              quantity: item.quantity,
              variant_title: item.variant.title,
              product_title: item.title,
            })
          }
        })
      }

      return products
    })

    // Validate that we have digital products to fulfill
    const hasDigitalProducts = transform({
      digitalProducts,
    }, (data) => data.digitalProducts.length > 0)

    // Use conditional execution for digital products only
    const digitalProductOrder = when(
      "check-digital-products-condition",
      hasDigitalProducts,
      (hasDigitalProducts) => {
        return hasDigitalProducts
      }
    ).then(() => {
      console.log('✅ Digital products found, proceeding with fulfillment')

      // Create digital product order record
      const { digital_product_order } = createDigitalProductOrderStep({
        order_id: input.order_id,
        digital_products: digitalProducts,
      })

      // Enhance digital product order with order details for notification
      const enhancedDigitalProductOrder = transform({
        digital_product_order,
        order,
      }, (data) => ({
        ...data.digital_product_order,
        order: data.order,
      }))

      // Send notification to customer
      sendDigitalOrderNotificationStep({
        digital_product_order: enhancedDigitalProductOrder,
      })

      // Mark fulfillment as shipped/delivered
      // This should automatically update the order's fulfillment status
      markFulfillmentShippedStep({
        fulfillment_id: input.fulfillment_id,
        order_id: input.order_id,
      })

      return digital_product_order
    })

    console.log('🎯 Digital order fulfillment workflow completed')

    return new WorkflowResponse({
      order,
      digital_product_order: digitalProductOrder,
      digital_products: digitalProducts,
      success: true,
      message: "Digital order fulfillment completed successfully",
    })
  }
)

export default fulfillDigitalOrderWorkflow
