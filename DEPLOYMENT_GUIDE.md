# 🚀 Hebrew Book Store - Deployment Guide

Руководство по развертыванию Hebrew Book Store в продакшене.

## 📋 Предварительные требования

- **Node.js** 18+ 
- **PostgreSQL** 14+
- **Redis** (опционально, для кеширования)
- **Домен** с SSL сертификатом
- **Сервер** с минимум 2GB RAM

## 🏗️ Архитектура продакшена

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Medusa.js     │    │   PostgreSQL    │
│   (Svelte)      │◄──►│   Backend       │◄──►│   Database      │
│   Port: 80/443  │    │   Port: 9000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Nginx/CDN     │    │   Admin Panel   │
│   (Static)      │    │   Port: 9000    │
└─────────────────┘    └─────────────────┘
```

## 🔧 Настройка сервера

### 1️⃣ Подготовка сервера

```bash
# Обновление системы
sudo apt update && sudo apt upgrade -y

# Установка Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Установка PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Установка Nginx
sudo apt install nginx -y

# Установка PM2 для управления процессами
sudo npm install -g pm2

# Установка Yarn
sudo npm install -g yarn
```

### 2️⃣ Настройка PostgreSQL

```bash
# Переключиться на пользователя postgres
sudo -u postgres psql

# Создать базу данных и пользователя
CREATE DATABASE hebrew_book_store;
CREATE USER medusa_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE hebrew_book_store TO medusa_user;
\q
```

### 3️⃣ Клонирование проекта

```bash
# Клонировать репозиторий
git clone https://github.com/your-username/hebrew-book-store.git
cd hebrew-book-store

# Установить зависимости backend
cd backend
yarn install

# Установить зависимости frontend
cd ../frontend
yarn install
```

## ⚙️ Настройка переменных окружения

### Backend (.env)

```bash
cd backend
cp .env.example .env
```

```env
# Database
DATABASE_URL=postgres://medusa_user:your_secure_password@localhost:5432/hebrew_book_store

# JWT
JWT_SECRET=your_super_secure_jwt_secret_here

# CORS
STORE_CORS=https://yourdomain.com,https://www.yourdomain.com
ADMIN_CORS=https://yourdomain.com:9000,https://admin.yourdomain.com

# Redis (опционально)
REDIS_URL=redis://localhost:6379

# Email (для уведомлений)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Medusa
MEDUSA_BACKEND_URL=https://yourdomain.com:9000
MEDUSA_ADMIN_BACKEND_URL=https://yourdomain.com:9000
```

### Frontend (.env.production)

```bash
cd frontend
cp .env.example .env.production
```

```env
# API URLs
VITE_BACKEND_URL=https://yourdomain.com:9000
VITE_MEDUSA_PUBLISHABLE_API_KEY=pk_your_publishable_key_here

# App settings
VITE_APP_NAME=Hebrew Book Store
VITE_APP_URL=https://yourdomain.com
```

## 🚀 Развертывание

### 1️⃣ Подготовка backend

```bash
cd backend

# Запуск миграций
yarn build
yarn medusa db:migrate

# Создание админа
yarn medusa user -e <EMAIL> -p your_secure_admin_password
```

### 2️⃣ Сборка frontend

```bash
cd frontend

# Сборка для продакшена
yarn build

# Копирование в директорию Nginx
sudo cp -r build/* /var/www/html/
```

### 3️⃣ Настройка PM2

Создать файл `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [
    {
      name: 'hebrew-book-backend',
      script: 'yarn',
      args: 'start',
      cwd: '/path/to/hebrew-book-store/backend',
      env: {
        NODE_ENV: 'production',
        PORT: 9000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G'
    }
  ]
}
```

Запуск:

```bash
# Запуск приложения
pm2 start ecosystem.config.js

# Сохранение конфигурации
pm2 save

# Автозапуск при перезагрузке
pm2 startup
```

### 4️⃣ Настройка Nginx

Создать файл `/etc/nginx/sites-available/hebrew-book-store`:

```nginx
# Frontend
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Redirect to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL certificates
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Frontend static files
    root /var/www/html;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy to backend
    location /store/ {
        proxy_pass http://localhost:9000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Backend/Admin
server {
    listen 9000 ssl http2;
    listen [::]:9000 ssl http2;
    server_name yourdomain.com;

    # SSL certificates
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:9000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Активация конфигурации:

```bash
# Создать символическую ссылку
sudo ln -s /etc/nginx/sites-available/hebrew-book-store /etc/nginx/sites-enabled/

# Проверить конфигурацию
sudo nginx -t

# Перезапустить Nginx
sudo systemctl restart nginx
```

## 🔑 Создание API ключей

### 1️⃣ Вход в админку

1. Откройте: `https://yourdomain.com:9000/app`
2. Войдите с созданными учетными данными

### 2️⃣ Создание Publishable API Key

1. Перейдите в **Settings** → **API Keys**
2. Нажмите **"Create API Key"**
3. Выберите тип: **"Publishable"**
4. Укажите название: **"Production Storefront Key"**
5. Сохраните ключ

### 3️⃣ Обновление переменных окружения

```bash
# Создать .env.production с API ключом
cd frontend
cat > .env.production << EOF
VITE_BACKEND_URL=https://yourdomain.com:9000
VITE_MEDUSA_PUBLISHABLE_API_KEY=pk_your_actual_key_here
VITE_APP_NAME=Hebrew Book Store
VITE_APP_URL=https://yourdomain.com
NODE_ENV=production
EOF

# Пересобрать frontend
yarn build
sudo cp -r build/* /var/www/html/
```

## 🔒 Безопасность

### 1️⃣ Firewall

```bash
# Настройка UFW
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 9000
sudo ufw enable
```

### 2️⃣ SSL сертификаты

```bash
# Установка Certbot
sudo apt install certbot python3-certbot-nginx

# Получение сертификата
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### 3️⃣ Регулярные обновления

```bash
# Создать cron job для обновлений
sudo crontab -e

# Добавить строку для еженедельных обновлений
0 2 * * 0 apt update && apt upgrade -y
```

## 📊 Мониторинг

### 1️⃣ PM2 мониторинг

```bash
# Статус приложений
pm2 status

# Логи
pm2 logs

# Мониторинг ресурсов
pm2 monit
```

### 2️⃣ Nginx логи

```bash
# Access логи
sudo tail -f /var/log/nginx/access.log

# Error логи
sudo tail -f /var/log/nginx/error.log
```

## 🔄 Обновление

### 1️⃣ Backend

```bash
cd backend
git pull origin main
yarn install
yarn build
pm2 restart hebrew-book-backend
```

### 2️⃣ Frontend

```bash
cd frontend
git pull origin main
yarn install
yarn build
sudo cp -r build/* /var/www/html/
```

## 🆘 Troubleshooting

### Проблема: Backend не запускается

```bash
# Проверить логи PM2
pm2 logs hebrew-book-backend

# Проверить переменные окружения
pm2 env 0

# Перезапустить
pm2 restart hebrew-book-backend
```

### Проблема: База данных недоступна

```bash
# Проверить статус PostgreSQL
sudo systemctl status postgresql

# Проверить подключение
psql -h localhost -U medusa_user -d hebrew_book_store
```

### Проблема: SSL сертификат

```bash
# Обновить сертификат
sudo certbot renew

# Проверить срок действия
sudo certbot certificates
```

## ✅ Чек-лист развертывания

- [ ] Сервер настроен и обновлен
- [ ] PostgreSQL установлен и настроен
- [ ] Проект склонирован и зависимости установлены
- [ ] Переменные окружения настроены
- [ ] Миграции базы данных выполнены
- [ ] Админ пользователь создан
- [ ] Backend собран и запущен через PM2
- [ ] Frontend собран и размещен в Nginx
- [ ] Nginx настроен и запущен
- [ ] SSL сертификаты установлены
- [ ] Firewall настроен
- [ ] API ключи созданы в админке
- [ ] Тестирование всех функций выполнено

🎉 **Поздравляем! Hebrew Book Store успешно развернут в продакшене!**
