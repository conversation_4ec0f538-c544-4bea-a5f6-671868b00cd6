import { 
  createStep, 
  StepResponse,
} from "@medusajs/framework/workflows-sdk"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import { DIGITAL_PRODUCT_MODULE } from "../../../modules/digital-product"
import DigitalProductModuleService from "../../../modules/digital-product/service"

type DeleteDigitalProductStepInput = {
  variant_id: string
}

export const deleteDigitalProductStep = createStep(
  "delete-digital-product-step",
  async (input: DeleteDigitalProductStepInput, { container }) => {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    const digitalProductModuleService: DigitalProductModuleService = 
      container.resolve(DIGITAL_PRODUCT_MODULE)

    const { data: digitalProducts } = await query.graph({
      entity: "digital_product",
      fields: ["id"],
      filters: {
        product_variant: {
          id: input.variant_id,
        },
      },
    })

    if (digitalProducts.length > 0) {
      await digitalProductModuleService.deleteDigitalProducts(
        digitalProducts.map((dp: any) => dp.id)
      )
    }

    return new StepResponse(digitalProducts)
  }
)
