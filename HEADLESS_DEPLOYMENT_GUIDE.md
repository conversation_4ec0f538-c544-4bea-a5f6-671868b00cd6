# Headless Deployment Guide

This guide covers deploying the Hebrew Book Store **headless architecture** to production environments with **separate backend and frontend deployments**.

## 🏗️ Headless Architecture Overview

The Hebrew Book Store consists of **two independent applications**:
- **🔧 Backend**: MedusaJS API server (can be deployed anywhere)
- **🎨 Frontend**: SvelteKit application (can be deployed anywhere)
- **📡 Communication**: RESTful API calls between them
- **🗄️ Database**: PostgreSQL (shared by backend only)

## 🚀 Deployment Strategy

### **Independent Deployment Benefits**
- **Separate scaling** - Scale backend and frontend independently
- **Technology flexibility** - Use different hosting providers
- **Development independence** - Teams can deploy separately
- **Cost optimization** - Choose optimal hosting for each component

### **Recommended Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (SvelteKit)   │◄──►│   (MedusaJS)    │◄──►│  (PostgreSQL)   │
│                 │    │                 │    │                 │
│ Vercel/Netlify  │    │ Railway/Heroku  │    │ Railway/AWS RDS │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Backend Deployment (MedusaJS)

### **1. Environment Setup**

Create production `.env` file:

```env
# Database
DATABASE_URL=************************************/database

# Redis (recommended for production)
REDIS_URL=redis://host:6379

# JWT Secret (generate a secure random string)
JWT_SECRET=your-super-secure-jwt-secret-here

# CORS Settings - IMPORTANT for headless setup
ADMIN_CORS=https://yourdomain.com,https://admin.yourdomain.com
STORE_CORS=https://yourstore.com,https://www.yourstore.com

# Stripe
STRIPE_API_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email
SENDGRID_API_KEY=SG...
SENDGRID_FROM=<EMAIL>

# File Storage (if using S3)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_BUCKET=your-bucket-name
```

### **2. Build and Deploy Backend**

```bash
cd backend

# Install dependencies
yarn install

# Build the application
yarn build

# Run migrations
yarn medusa db:migrate

# Start production server
yarn start
```

### **3. Backend Hosting Options**

#### **Railway (Recommended)**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway link
railway up
```

**Benefits**: Automatic PostgreSQL, Redis, easy scaling, great for MedusaJS

#### **Heroku**
```bash
# Create app
heroku create your-backend-api

# Add PostgreSQL and Redis
heroku addons:create heroku-postgresql:hobby-dev
heroku addons:create heroku-redis:hobby-dev

# Set environment variables
heroku config:set JWT_SECRET=your-secret
heroku config:set STRIPE_API_KEY=sk_live_...

# Deploy
git subtree push --prefix backend heroku main
```

#### **DigitalOcean App Platform**
1. Create new app from GitHub
2. Set root directory to `/backend`
3. Configure environment variables
4. Add managed PostgreSQL database

### **4. Backend Domain Setup**
- **API Domain**: `api.yourdomain.com`
- **Admin Panel**: `api.yourdomain.com/app`
- **Health Check**: `api.yourdomain.com/health`

## 🎨 Frontend Deployment (SvelteKit)

### **1. Environment Setup**

Create production `.env.local`:

```env
# Backend API URL - CRITICAL for headless setup
VITE_API_BASE_URL=https://api.yourdomain.com

# MedusaJS API Keys
VITE_MEDUSA_PUBLISHABLE_KEY=pk_live_...

# Stripe
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...

# Default Region and Sales Channel
VITE_DEFAULT_REGION_ID=reg_01...
VITE_DEFAULT_SALES_CHANNEL_ID=sc_01...
```

### **2. Build and Deploy Frontend**

```bash
cd frontend

# Install dependencies
yarn install

# Build the application
yarn build

# Preview (optional)
yarn preview
```

### **3. Frontend Hosting Options**

#### **Vercel (Recommended)**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy from frontend directory
cd frontend
vercel

# Set environment variables in Vercel dashboard
```

**Benefits**: Zero-config SvelteKit deployment, automatic SSL, global CDN

#### **Netlify**
1. Connect GitHub repository
2. Set base directory: `frontend`
3. Set build command: `yarn build`
4. Set publish directory: `frontend/build`
5. Configure environment variables

#### **Cloudflare Pages**
1. Connect GitHub repository
2. Set build command: `cd frontend && yarn install && yarn build`
3. Set output directory: `frontend/build`
4. Configure environment variables

### **4. Frontend Domain Setup**
- **Store Frontend**: `yourdomain.com` or `www.yourdomain.com`
- **Customer Library**: `yourdomain.com/library`
- **Customer Account**: `yourdomain.com/account`

## 🗄️ Database Deployment

### **PostgreSQL Hosting Options**

#### **Railway PostgreSQL (Recommended)**
- Automatic backups
- Connection pooling
- Easy scaling
- Integrated with backend deployment

#### **AWS RDS**
```bash
# Create RDS instance
aws rds create-db-instance \
  --db-instance-identifier hebrew-book-store \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username medusa_user \
  --master-user-password secure_password \
  --allocated-storage 20
```

#### **Heroku Postgres**
```bash
# Add to existing Heroku app
heroku addons:create heroku-postgresql:hobby-dev

# Get connection string
heroku config:get DATABASE_URL
```

## 🌐 Domain and SSL Configuration

### **1. DNS Configuration**

```
# A Records
api.yourdomain.com     → Backend hosting IP
yourdomain.com         → Frontend hosting IP
www.yourdomain.com     → Frontend hosting IP

# CNAME Records (if using hosting CDNs)
api.yourdomain.com     → your-backend.railway.app
yourdomain.com         → your-frontend.vercel.app
```

### **2. SSL Certificates**

Most hosting platforms provide automatic SSL:
- **Vercel**: Automatic SSL for custom domains
- **Netlify**: Automatic SSL with Let's Encrypt
- **Railway**: Automatic SSL for custom domains
- **Heroku**: SSL available with paid plans

### **3. CORS Configuration**

**Critical for headless setup** - Update backend CORS:

```typescript
// backend/medusa-config.ts
const ADMIN_CORS = process.env.ADMIN_CORS || 
  "https://yourdomain.com,https://api.yourdomain.com"
const STORE_CORS = process.env.STORE_CORS || 
  "https://yourdomain.com,https://www.yourdomain.com"

export default defineConfig({
  projectConfig: {
    http: {
      adminCors: ADMIN_CORS,
      storeCors: STORE_CORS,
    },
  },
})
```

## 📧 Email Configuration

### **SendGrid Setup**

```env
# Backend .env
SENDGRID_API_KEY=SG.your-api-key
SENDGRID_FROM=<EMAIL>
```

### **Email Templates**
Configure in MedusaJS for:
- Order confirmation
- Digital product delivery
- Password reset
- Customer registration

## 🔒 Security for Headless Architecture

### **1. API Security**
```env
# Strong JWT secret
JWT_SECRET=super-long-random-string-here

# Secure API keys
STRIPE_API_KEY=sk_live_...
MEDUSA_PUBLISHABLE_KEY=pk_live_...
```

### **2. CORS Security**
```typescript
// Only allow your frontend domains
const STORE_CORS = "https://yourdomain.com,https://www.yourdomain.com"
```

### **3. Environment Variables**
- **Backend**: Database, API keys, secrets
- **Frontend**: Public API URLs and public keys only
- **Never expose**: Database URLs, private API keys in frontend

## 📊 Monitoring Headless Architecture

### **Backend Monitoring**
```bash
# Health check endpoint
curl https://api.yourdomain.com/health

# API response monitoring
curl https://api.yourdomain.com/store/products
```

### **Frontend Monitoring**
```bash
# Frontend availability
curl https://yourdomain.com

# API connectivity from frontend
# Check browser network tab for API calls
```

### **Database Monitoring**
- Connection pool status
- Query performance
- Storage usage
- Backup status

## 🚀 Scaling Headless Architecture

### **Independent Scaling**

#### **Scale Backend Only**
```bash
# Railway
railway scale --replicas 3

# Heroku
heroku ps:scale web=3
```

#### **Scale Frontend Only**
- Vercel: Automatic global CDN scaling
- Netlify: Automatic edge distribution
- Cloudflare: Global edge network

#### **Scale Database**
```bash
# Upgrade database plan
railway database:upgrade
heroku addons:upgrade heroku-postgresql:standard-0
```

### **Performance Optimization**

#### **Backend Optimization**
- Enable Redis caching
- Database query optimization
- API response caching
- Gzip compression

#### **Frontend Optimization**
- Code splitting
- Image optimization
- Service worker caching
- Bundle size optimization

## 🔧 Deployment Scripts

### **Backend Deployment Script**
```bash
#!/bin/bash
# deploy-backend.sh

cd backend
echo "Installing dependencies..."
yarn install

echo "Building application..."
yarn build

echo "Running migrations..."
yarn medusa db:migrate

echo "Deploying to Railway..."
railway up

echo "Backend deployed successfully!"
```

### **Frontend Deployment Script**
```bash
#!/bin/bash
# deploy-frontend.sh

cd frontend
echo "Installing dependencies..."
yarn install

echo "Building application..."
yarn build

echo "Deploying to Vercel..."
vercel --prod

echo "Frontend deployed successfully!"
```

## 🆘 Troubleshooting Headless Setup

### **Common Issues**

#### **CORS Errors**
```
Access to fetch at 'https://api.yourdomain.com' from origin 'https://yourdomain.com' 
has been blocked by CORS policy
```
**Solution**: Update `STORE_CORS` in backend environment variables

#### **API Connection Failed**
```
Failed to fetch from https://api.yourdomain.com/store/products
```
**Solution**: Check `VITE_API_BASE_URL` in frontend environment variables

#### **Authentication Issues**
```
401 Unauthorized
```
**Solution**: Verify `VITE_MEDUSA_PUBLISHABLE_KEY` is correct

### **Debugging Steps**

1. **Check backend health**: `curl https://api.yourdomain.com/health`
2. **Verify CORS settings**: Check browser network tab
3. **Test API endpoints**: Use Postman or curl
4. **Check environment variables**: Verify all required vars are set
5. **Review logs**: Check hosting platform logs

## 📋 Deployment Checklist

### **Pre-Deployment**
- [ ] Backend environment variables configured
- [ ] Frontend environment variables configured
- [ ] Database created and accessible
- [ ] Domain names configured
- [ ] SSL certificates ready

### **Backend Deployment**
- [ ] Dependencies installed
- [ ] Application built successfully
- [ ] Database migrations run
- [ ] Health check endpoint working
- [ ] Admin panel accessible
- [ ] API endpoints responding

### **Frontend Deployment**
- [ ] Dependencies installed
- [ ] Application built successfully
- [ ] Environment variables set
- [ ] API connectivity working
- [ ] Pages loading correctly
- [ ] Authentication working

### **Post-Deployment**
- [ ] End-to-end testing
- [ ] Performance monitoring setup
- [ ] Error tracking configured
- [ ] Backup procedures verified
- [ ] Documentation updated

---

**🎯 Your headless e-commerce platform is now deployed with complete independence between backend and frontend!**
