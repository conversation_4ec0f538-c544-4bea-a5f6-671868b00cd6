import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import {
  ContainerRegistrationKeys,
  ModuleRegistrationName,
} from "@medusajs/framework/utils"
import fulfillDigitalOrderWorkflow from "../../../../../workflows/fulfill-digital-order"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log('🔔 MANUAL DIGITAL FULFILLMENT ENDPOINT CALLED')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('🎯 Order ID:', req.params.id)

  try {
    const orderId = req.params.id
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

    // Get order details with digital products and fulfillments
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "items.*",
        "items.variant.*",
        "items.variant.digital_product.*",
        "fulfillments.*",
      ],
      filters: {
        id: orderId,
      },
    })

    if (!orders || orders.length === 0) {
      return res.status(404).json({
        error: "Order not found",
        order_id: orderId,
      })
    }

    const order = orders[0]
    console.log('📦 ORDER DETAILS:')
    console.log('  - Order ID:', order.id)
    console.log('  - Order Status:', order.status)
    console.log('  - Items Count:', order.items?.length || 0)
    console.log('  - Fulfillments Count:', order.fulfillments?.length || 0)

    // Check if order has digital products
    const digitalProducts: any[] = []
    
    if (order.items) {
      order.items.forEach((item: any) => {
        if (item?.variant?.digital_product) {
          digitalProducts.push({
            id: item.variant.digital_product.id,
            name: item.variant.digital_product.name,
            variant_title: item.variant.title,
            product_title: item.title,
            quantity: item.quantity,
          })
        }
      })
    }

    console.log('🔍 DIGITAL PRODUCTS ANALYSIS:')
    console.log('  - Digital Products Found:', digitalProducts.length)
    console.log('  - Digital Products:', digitalProducts.map(p => ({ id: p.id, name: p.name })))

    if (digitalProducts.length === 0) {
      return res.status(400).json({
        error: "No digital products found in this order",
        order_id: orderId,
        digital_products_count: 0,
      })
    }

    // Find the most recent fulfillment (or create a mock one if none exists)
    let fulfillmentId: string = ""
    
    if (order.fulfillments && order.fulfillments.length > 0) {
      // Use the most recent fulfillment
      fulfillmentId = order.fulfillments[order.fulfillments.length - 1].id
      console.log('📦 Using existing fulfillment:', fulfillmentId)
    } else {
      // If no fulfillments exist, we'll use a placeholder
      fulfillmentId = `manual_${orderId}_${Date.now()}`
      console.log('📦 No fulfillments found, using placeholder:', fulfillmentId)
    }

    console.log('🚀 STARTING MANUAL DIGITAL FULFILLMENT WORKFLOW')
    console.log('  - Order ID:', orderId)
    console.log('  - Fulfillment ID:', fulfillmentId)
    console.log('  - Digital Products Count:', digitalProducts.length)

    // Run the digital order fulfillment workflow
    const { result } = await fulfillDigitalOrderWorkflow(req.scope).run({
      input: {
        order_id: orderId,
        fulfillment_id: fulfillmentId,
      },
    })

    console.log('✅ MANUAL DIGITAL FULFILLMENT COMPLETED')
    console.log('  - Order ID:', result?.order?.id)
    console.log('  - Digital Product Order ID:', result?.digital_product_order?.id)
    console.log('  - Digital Products Processed:', result?.digital_products?.length || 0)

    res.json({
      success: true,
      message: "Manual digital fulfillment completed successfully",
      order_id: orderId,
      fulfillment_id: fulfillmentId,
      digital_products_count: digitalProducts.length,
      digital_product_order_id: result?.digital_product_order?.id,
      workflow_result: {
        order: result?.order?.id,
        digital_products: result?.digital_products?.length || 0,
      },
    })

  } catch (error) {
    console.error('❌ CRITICAL ERROR IN MANUAL DIGITAL FULFILLMENT')
    console.error('  - Order ID:', req.params.id)
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack Trace:', error.stack)
    console.error('  - Timestamp:', new Date().toISOString())

    res.status(500).json({
      error: "Failed to fulfill digital order",
      order_id: req.params.id,
      error_message: error.message,
      error_type: error.constructor.name,
      timestamp: new Date().toISOString(),
    })
  }
}
