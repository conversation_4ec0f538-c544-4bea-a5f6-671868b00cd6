import type {
  SubscriberConfig,
  SubscriberArgs,
} from "@medusajs/framework"
import {
  ContainerRegistrationKeys,
  ModuleRegistrationName,
} from "@medusajs/framework/utils"
import fulfillDigitalOrderWorkflow from "../workflows/fulfill-digital-order"

// Subscriber that automatically fulfills digital orders when fulfillment is created
export default async function autoFulfillDigitalOrders({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; order_id: string }>) {
  console.log('🔔 AUTO-FULFILL DIGITAL ORDERS - Fulfillment created event received')
  console.log('📋 Event Data:', JSON.stringify(data, null, 2))
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('🎯 Fulfillment ID:', data.id)
  console.log('🎯 Order ID:', data.order_id)

  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)

    // Get fulfillment details to check if it's for digital products
    const { data: fulfillments } = await query.graph({
      entity: "fulfillment",
      fields: [
        "*",
        "items.*",
        "items.item.*",
        "items.item.variant.*",
        "items.item.variant.digital_product.*",
        "order.*",
        "order.items.*",
        "order.items.variant.*",
        "order.items.variant.digital_product.*",
      ],
      filters: {
        id: data.id,
      },
    })

    if (!fulfillments || fulfillments.length === 0) {
      console.log('❌ Fulfillment not found:', data.id)
      return
    }

    const fulfillment = fulfillments[0]
    console.log('📦 FULFILLMENT DETAILS:')
    console.log('  - Fulfillment ID:', fulfillment.id)
    console.log('  - Order ID:', fulfillment.order?.id)
    console.log('  - Provider ID:', fulfillment.provider_id)
    console.log('  - Items Count:', fulfillment.items?.length || 0)

    // Check if this fulfillment contains digital products
    let hasDigitalProducts = false
    
    if (fulfillment.items) {
      fulfillment.items.forEach((fulfillmentItem: any, index: number) => {
        console.log(`  📋 Fulfillment Item ${index + 1}:`)
        console.log('    - Item ID:', fulfillmentItem.item?.id)
        console.log('    - Variant ID:', fulfillmentItem.item?.variant?.id)
        console.log('    - Has Digital Product:', !!fulfillmentItem.item?.variant?.digital_product)
        
        if (fulfillmentItem.item?.variant?.digital_product) {
          hasDigitalProducts = true
          console.log('    - Digital Product ID:', fulfillmentItem.item.variant.digital_product.id)
          console.log('    - Digital Product Name:', fulfillmentItem.item.variant.digital_product.name)
        }
      })
    }

    // Also check if the fulfillment is from digital fulfillment provider
    const isDigitalFulfillment = fulfillment.provider_id === 'digital-fulfillment' || 
                                fulfillment.provider_id === 'digital'

    console.log('🔍 DIGITAL PRODUCTS ANALYSIS:')
    console.log('  - Has Digital Products:', hasDigitalProducts)
    console.log('  - Is Digital Fulfillment Provider:', isDigitalFulfillment)
    console.log('  - Provider ID:', fulfillment.provider_id)

    if (!hasDigitalProducts && !isDigitalFulfillment) {
      console.log('ℹ️ No digital products in fulfillment - skipping auto-fulfillment')
      return
    }

    console.log('🚀 DIGITAL FULFILLMENT DETECTED - STARTING WORKFLOW')
    console.log('  - Order ID:', fulfillment.order?.id)
    console.log('  - Fulfillment ID:', fulfillment.id)

    // Run the digital order fulfillment workflow
    const { result } = await fulfillDigitalOrderWorkflow(container).run({
      input: {
        order_id: fulfillment.order?.id || data.order_id,
        fulfillment_id: fulfillment.id,
      },
    })

    console.log('✅ AUTO-FULFILLMENT WORKFLOW COMPLETED')
    console.log('  - Order ID:', result?.order?.id)
    console.log('  - Digital Product Order ID:', result?.digital_product_order?.id)
    console.log('  - Digital Products Count:', result?.digital_products?.length || 0)

  } catch (error) {
    console.error('❌ CRITICAL ERROR IN AUTO-FULFILL DIGITAL ORDERS')
    console.error('  - Fulfillment ID:', data.id)
    console.error('  - Order ID:', data.order_id)
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack Trace:', error.stack)
    console.error('  - Timestamp:', new Date().toISOString())

    // Don't throw error to prevent fulfillment creation failure
    // Auto-fulfillment can be handled manually if needed
  }
}

// DISABLED: This subscriber is disabled to avoid conflicts with digital-order-fulfillment.ts
// which handles everything when an order is placed
// export const config: SubscriberConfig = {
//   event: "fulfillment.created",
// }
