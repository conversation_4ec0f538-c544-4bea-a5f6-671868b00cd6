import { MedusaApp } from "@medusajs/modules-sdk"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

async function testOrderStatus() {
  console.log('🔍 Testing Order Status and Digital Product Detection')
  console.log('⏰ Timestamp:', new Date().toISOString())

  try {
    const app = await MedusaApp({
      directory: process.cwd(),
    })

    const container = app.getContainer()
    const query = container.resolve(ContainerRegistrationKeys.QUERY)

    // Get all orders with their items and fulfillments
    console.log('\n📋 Fetching all orders...')
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "items.*",
        "items.variant.*",
        "items.variant.digital_product.*",
        "fulfillments.*",
        "customer.*",
      ],
      pagination: {
        take: 10,
        order: {
          created_at: "DESC",
        },
      },
    })

    console.log(`\n📊 Found ${orders.length} orders`)

    for (const order of orders) {
      console.log(`\n🛒 ORDER: ${order.id}`)
      console.log(`  - Status: ${order.status}`)
      console.log(`  - Customer: ${order.customer?.email || 'Guest'}`)
      console.log(`  - Created: ${order.created_at}`)
      console.log(`  - Items: ${order.items?.length || 0}`)
      console.log(`  - Fulfillments: ${order.fulfillments?.length || 0}`)

      // Check for digital products
      let hasDigitalProducts = false
      if (order.items) {
        for (const item of order.items) {
          if (item.variant?.digital_product) {
            hasDigitalProducts = true
            console.log(`    📱 DIGITAL PRODUCT FOUND:`)
            console.log(`      - Item: ${item.title}`)
            console.log(`      - Variant: ${item.variant.title}`)
            console.log(`      - Digital Product: ${item.variant.digital_product.name}`)
            console.log(`      - Quantity: ${item.quantity}`)
          }
        }
      }

      if (hasDigitalProducts) {
        console.log(`  ✅ HAS DIGITAL PRODUCTS`)
        
        // Check fulfillment status
        if (order.fulfillments && order.fulfillments.length > 0) {
          for (const fulfillment of order.fulfillments) {
            console.log(`    📦 Fulfillment: ${fulfillment.id}`)
            console.log(`      - Provider: ${fulfillment.provider_id}`)
            console.log(`      - Shipped At: ${fulfillment.shipped_at || 'Not shipped'}`)
            console.log(`      - Created At: ${fulfillment.created_at}`)
          }
        } else {
          console.log(`    ⚠️ NO FULFILLMENTS FOUND`)
        }
      } else {
        console.log(`  ❌ No digital products`)
      }
    }

    // Check digital product orders
    console.log('\n📱 Checking Digital Product Orders...')
    try {
      const digitalProductService = container.resolve("digitalProductFulfillmentService")
      const digitalOrders = await digitalProductService.list({})
      console.log(`📊 Found ${digitalOrders.length} digital product orders`)
      
      for (const digitalOrder of digitalOrders) {
        console.log(`  📱 Digital Order: ${digitalOrder.id}`)
        console.log(`    - Order ID: ${digitalOrder.order_id}`)
        console.log(`    - Status: ${digitalOrder.status}`)
        console.log(`    - Created: ${digitalOrder.created_at}`)
      }
    } catch (error) {
      console.log(`❌ Error fetching digital product orders: ${error.message}`)
    }

    await app.shutdown()
    console.log('\n✅ Test completed')

  } catch (error) {
    console.error('❌ CRITICAL ERROR IN ORDER STATUS TEST')
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack Trace:', error.stack)
    process.exit(1)
  }
}

testOrderStatus()
