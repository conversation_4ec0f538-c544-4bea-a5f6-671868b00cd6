import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Get the product module service
    const productModuleService = req.scope.resolve("product")

    // Get products with all fields including metadata
    const products = await productModuleService.list({}, {
      select: [
        "id",
        "title",
        "description",
        "handle",
        "status",
        "metadata",
        "created_at",
        "updated_at"
      ],
      relations: ["variants", "tags", "type", "collection", "images"],
      take: 5 // Limit to first 5 products for debugging
    })

    console.log("🔍 Debug: Raw products from database:", JSON.stringify(products, null, 2))

    // Return the raw products for inspection
    res.json({
      message: "Debug: Raw products from MedusaJS",
      count: products.length,
      products: products,
      first_product_metadata: products[0]?.metadata || null
    })

  } catch (error) {
    console.error("❌ Debug endpoint error:", error)
    res.status(500).json({
      error: "Failed to fetch products",
      details: error.message
    })
  }
}
